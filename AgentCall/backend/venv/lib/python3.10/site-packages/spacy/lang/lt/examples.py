"""
Example sentences to test spaCy and its language models.

>>> from spacy.lang.lt.examples import sentences
>>> docs = nlp.pipe(sentences)
"""


sentences = [
    "Jaunikis pirmąją vestuvinę naktį iškeitė į areštinės gultą",
    "Bepiločiai automobiliai išnaikins vair<PERSON><PERSON>, autoservisus ir eismo nelaimes",
    "Vilniuje galvojama uždrausti naudoti skėčius",
    "Londonas yra didelis miestas Jungtinėje Karalystėje",
    "Kur tu?",
    "Kas yra Prancūzijos prezidentas?",
    "Kokia yra Jungtinių Amerikos Valstijų sostinė?",
    "Kada gimė Dalia Grybauskaitė?",
]
