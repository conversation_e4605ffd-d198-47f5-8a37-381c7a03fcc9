#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API rename_ {
  using schema = at::Tensor & (at::Tensor &, ::std::optional<at::DimnameList>);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::rename_";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "rename_(Tensor(a!) self, Dimname[]? names) -> Tensor(a!)";
  static at::Tensor & call(at::Tensor & self, ::std::optional<at::DimnameList> names);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, at::Tensor & self, ::std::optional<at::DimnameList> names);
};

struct TORCH_API rename {
  using schema = at::Tensor (const at::Tensor &, ::std::optional<at::DimnameList>);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::rename";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "rename(Tensor(a) self, Dimname[]? names) -> Tensor(a)";
  static at::Tensor call(const at::Tensor & self, ::std::optional<at::DimnameList> names);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, ::std::optional<at::DimnameList> names);
};

}} // namespace at::_ops
