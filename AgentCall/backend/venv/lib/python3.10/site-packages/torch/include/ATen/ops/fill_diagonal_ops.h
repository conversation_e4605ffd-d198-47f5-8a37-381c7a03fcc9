#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API fill_diagonal_ {
  using schema = at::Tensor & (at::Tensor &, const at::Scalar &, bool);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::fill_diagonal_";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "fill_diagonal_(Tensor(a!) self, Scalar fill_value, bool wrap=False) -> Tensor(a!)";
  static at::Tensor & call(at::Tensor & self, const at::<PERSON>ala<PERSON> & fill_value, bool wrap);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, at::Tensor & self, const at::Scalar & fill_value, bool wrap);
};

}} // namespace at::_ops
