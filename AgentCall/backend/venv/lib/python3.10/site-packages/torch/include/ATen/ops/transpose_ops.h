#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API transpose_int {
  using schema = at::Tensor (const at::Tensor &, int64_t, int64_t);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::transpose";
  static constexpr const char* overload_name = "int";
  static constexpr const char* schema_str = "transpose.int(Tensor(a) self, int dim0, int dim1) -> Tensor(a)";
  static at::Tensor call(const at::Tensor & self, int64_t dim0, int64_t dim1);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, int64_t dim0, int64_t dim1);
};

struct TORCH_API transpose_Dimname {
  using schema = at::Tensor (const at::Tensor &, at::Dimname, at::Dimname);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::transpose";
  static constexpr const char* overload_name = "Dimname";
  static constexpr const char* schema_str = "transpose.Dimname(Tensor(a) self, Dimname dim0, Dimname dim1) -> Tensor(a)";
  static at::Tensor call(const at::Tensor & self, at::Dimname dim0, at::Dimname dim1);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, at::Dimname dim0, at::Dimname dim1);
};

struct TORCH_API transpose_ {
  using schema = at::Tensor & (at::Tensor &, int64_t, int64_t);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::transpose_";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "transpose_(Tensor(a!) self, int dim0, int dim1) -> Tensor(a!)";
  static at::Tensor & call(at::Tensor & self, int64_t dim0, int64_t dim1);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, at::Tensor & self, int64_t dim0, int64_t dim1);
};

}} // namespace at::_ops
