#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API _has_compatible_shallow_copy_type {
  using schema = bool (const at::Tensor &, const at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_has_compatible_shallow_copy_type";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "_has_compatible_shallow_copy_type(Tensor self, Tensor from) -> bool";
  static bool call(const at::Tensor & self, const at::Tensor & from);
  static bool redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Tensor & from);
};

}} // namespace at::_ops
