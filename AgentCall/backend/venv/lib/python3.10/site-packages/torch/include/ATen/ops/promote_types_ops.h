#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API promote_types {
  using schema = at::ScalarType (at::ScalarType, at::ScalarType);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::promote_types";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "promote_types(ScalarType type1, ScalarType type2) -> ScalarType";
  static at::ScalarType call(at::ScalarType type1, at::ScalarType type2);
  static at::ScalarType redispatch(c10::DispatchKeySet dispatchKeySet, at::ScalarType type1, at::ScalarType type2);
};

}} // namespace at::_ops
