
# This file was generated by 'versioneer.py' (0.19) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2021-12-30T14:37:35-0700",
 "dirty": false,
 "error": null,
 "full-revisionid": "4adc87dff15a247e417d50f10cc4def8e1c17a03",
 "version": "1.22.0"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
