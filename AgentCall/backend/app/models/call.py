from sqlalchemy import Column, Integer, String, DateTime, Text, Float, <PERSON>ole<PERSON>, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base
from datetime import datetime
from typing import Optional


class Call(Base):
    """Model for storing call information and logs"""
    __tablename__ = "calls"

    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(255), unique=True, index=True, nullable=False)
    caller_number = Column(String(50), nullable=True)  # For traditional phone calls
    caller_name = Column(String(255), nullable=True)
    caller_email = Column(String(255), nullable=True)
    
    # Call metadata
    start_time = Column(DateTime(timezone=True), server_default=func.now())
    end_time = Column(DateTime(timezone=True), nullable=True)
    duration_seconds = Column(Float, nullable=True)
    call_type = Column(String(50), default="webrtc")  # webrtc, sip, asterisk
    
    # Call status and quality
    status = Column(String(50), default="initiated")  # initiated, connected, ended, failed
    connection_quality = Column(String(50), nullable=True)  # excellent, good, fair, poor
    
    # Audio and processing info
    audio_format = Column(String(50), nullable=True)
    sample_rate = Column(Integer, default=16000)
    total_audio_duration = Column(Float, nullable=True)
    
    # AI and processing statistics
    total_messages = Column(Integer, default=0)
    stt_processing_time = Column(Float, nullable=True)
    llm_processing_time = Column(Float, nullable=True)
    tts_processing_time = Column(Float, nullable=True)
    
    # Call summary and outcome
    conversation_summary = Column(Text, nullable=True)
    conversation_history = Column(JSON, nullable=True)  # Store conversation as JSON
    call_outcome = Column(String(100), nullable=True)  # appointment_booked, info_provided, callback_requested
    follow_up_required = Column(Boolean, default=False)
    
    # Relationships
    messages = relationship("Message", back_populates="call", cascade="all, delete-orphan")
    appointments = relationship("Appointment", back_populates="call", cascade="all, delete-orphan")
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<Call(id={self.id}, session_id='{self.session_id}', status='{self.status}')>"
    
    @property
    def duration_formatted(self) -> Optional[str]:
        """Get formatted duration string"""
        if not self.duration_seconds:
            return None
        
        minutes = int(self.duration_seconds // 60)
        seconds = int(self.duration_seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"
    
    def calculate_duration(self):
        """Calculate and set call duration"""
        if self.start_time and self.end_time:
            delta = self.end_time - self.start_time
            self.duration_seconds = delta.total_seconds()
    
    def end_call(self):
        """Mark call as ended and calculate duration"""
        if not self.end_time:
            self.end_time = datetime.utcnow()
            self.status = "ended"
            self.calculate_duration()
