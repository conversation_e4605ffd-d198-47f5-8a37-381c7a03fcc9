from sqlalchemy import Column, Integer, String, DateTime, Text, Float, Boolean, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base
from typing import Optional, Dict, Any


class Message(Base):
    """Model for storing conversation messages during calls"""
    __tablename__ = "messages"

    id = Column(Integer, primary_key=True, index=True)
    call_id = Column(Integer, ForeignKey("calls.id"), nullable=False, index=True)
    
    # Message content
    content = Column(Text, nullable=False)
    message_type = Column(String(50), nullable=False)  # user_speech, ai_response, system_message
    
    # Message metadata
    sender = Column(String(100), default="user")  # user, ai, system
    language = Column(String(10), default="en")
    confidence_score = Column(Float, nullable=True)  # For STT confidence
    
    # Processing information
    stt_processing_time = Column(Float, nullable=True)
    llm_processing_time = Column(Float, nullable=True)
    tts_processing_time = Column(Float, nullable=True)
    
    # Audio metadata (for voice messages)
    audio_duration = Column(Float, nullable=True)
    audio_format = Column(String(50), nullable=True)
    audio_file_path = Column(String(500), nullable=True)
    
    # Intent and context
    detected_intent = Column(String(100), nullable=True)
    intent_confidence = Column(Float, nullable=True)
    entities = Column(JSON, nullable=True)  # Extracted entities from NLP
    
    # Message status
    is_processed = Column(Boolean, default=False)
    has_response = Column(Boolean, default=False)
    is_transcription = Column(Boolean, default=False)
    
    # Additional metadata
    extra_data = Column(JSON, nullable=True)  # Flexible additional data
    
    # Timestamps
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    processed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    call = relationship("Call", back_populates="messages")

    def __repr__(self):
        return f"<Message(id={self.id}, type='{self.message_type}', sender='{self.sender}')>"
    
    @property
    def content_preview(self) -> str:
        """Get truncated content for preview"""
        if not self.content:
            return ""
        return self.content[:100] + "..." if len(self.content) > 100 else self.content
    
    @property
    def total_processing_time(self) -> Optional[float]:
        """Get total processing time across all stages"""
        total = 0
        if self.stt_processing_time:
            total += self.stt_processing_time
        if self.llm_processing_time:
            total += self.llm_processing_time
        if self.tts_processing_time:
            total += self.tts_processing_time
        return total if total > 0 else None
    
    def set_metadata(self, key: str, value: Any) -> None:
        """Set a metadata value"""
        if not self.extra_data:
            self.extra_data = {}
        self.extra_data[key] = value
    
    def get_metadata(self, key: str, default: Any = None) -> Any:
        """Get a metadata value"""
        if not self.extra_data:
            return default
        return self.extra_data.get(key, default)
    
    def add_entity(self, entity_type: str, entity_value: str, confidence: float = None) -> None:
        """Add an extracted entity"""
        if not self.entities:
            self.entities = []
        
        entity = {
            "type": entity_type,
            "value": entity_value,
            "confidence": confidence
        }
        self.entities.append(entity)
    
    def get_entities_by_type(self, entity_type: str) -> list:
        """Get all entities of a specific type"""
        if not self.entities:
            return []
        return [e for e in self.entities if e.get("type") == entity_type]
    
    def mark_processed(self) -> None:
        """Mark message as processed"""
        self.is_processed = True
        self.processed_at = func.now()
    
    @classmethod
    def create_user_message(cls, call_id: int, content: str, confidence_score: float = None) -> "Message":
        """Create a user speech message"""
        return cls(
            call_id=call_id,
            content=content,
            message_type="user_speech",
            sender="user",
            confidence_score=confidence_score,
            is_transcription=True
        )
    
    @classmethod
    def create_ai_response(cls, call_id: int, content: str, intent: str = None) -> "Message":
        """Create an AI response message"""
        return cls(
            call_id=call_id,
            content=content,
            message_type="ai_response",
            sender="ai",
            detected_intent=intent,
            has_response=True
        )
    
    @classmethod
    def create_system_message(cls, call_id: int, content: str, extra_data: Dict = None) -> "Message":
        """Create a system message"""
        return cls(
            call_id=call_id,
            content=content,
            message_type="system_message",
            sender="system",
            extra_data=extra_data
        )

    def to_dict(self):
        """Convert model to dictionary"""
        return {
            'id': self.id,
            'call_id': self.call_id,
            'content': self.content,
            'message_type': self.message_type,
            'sender': self.sender,
            'language': self.language,
            'confidence_score': self.confidence_score,
            'stt_processing_time': self.stt_processing_time,
            'llm_processing_time': self.llm_processing_time,
            'tts_processing_time': self.tts_processing_time,
            'audio_duration': self.audio_duration,
            'audio_format': self.audio_format,
            'audio_file_path': self.audio_file_path,
            'detected_intent': self.detected_intent,
            'intent_confidence': self.intent_confidence,
            'entities': self.entities,
            'is_processed': self.is_processed,
            'has_response': self.has_response,
            'is_transcription': self.is_transcription,
            'extra_data': self.extra_data,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'processed_at': self.processed_at.isoformat() if self.processed_at else None,
            'content_preview': self.content_preview,
            'total_processing_time': self.total_processing_time
        }
