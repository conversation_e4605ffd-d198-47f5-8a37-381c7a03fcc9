from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base
from typing import Optional, Dict, Any


class User(Base):
    """Model for storing user/caller information"""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    
    # Primary identification
    phone_number = Column(String(50), unique=True, index=True, nullable=True)
    email = Column(String(255), unique=True, index=True, nullable=True)
    
    # Personal information
    first_name = Column(String(255), nullable=True)
    last_name = Column(String(255), nullable=True)
    full_name = Column(String(500), nullable=True)
    
    # Contact preferences
    preferred_contact_method = Column(String(50), default="phone")  # phone, email, sms
    timezone = Column(String(100), nullable=True)
    language = Column(String(10), default="en")
    
    # User status and preferences
    is_active = Column(Boolean, default=True)
    is_blacklisted = Column(Boolean, default=False)
    allow_marketing = Column(Boolean, default=True)
    
    # User history and stats
    total_calls = Column(Integer, default=0)
    total_appointments = Column(Integer, default=0)
    last_call_date = Column(DateTime(timezone=True), nullable=True)
    
    # User profile and notes
    notes = Column(Text, nullable=True)
    tags = Column(JSON, nullable=True)  # List of tags like ["vip", "callback_requested"]
    custom_fields = Column(JSON, nullable=True)  # Flexible additional data
    
    # AI conversation context
    conversation_history_summary = Column(Text, nullable=True)
    preferred_communication_style = Column(String(100), nullable=True)  # formal, casual, technical
    
    # CRM integration fields
    external_crm_id = Column(String(255), nullable=True)
    lead_source = Column(String(100), nullable=True)  # website, referral, advertising
    lead_status = Column(String(100), default="new")  # new, contacted, qualified, converted
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    last_contacted_at = Column(DateTime(timezone=True), nullable=True)

    def __repr__(self):
        return f"<User(id={self.id}, name='{self.full_name}', phone='{self.phone_number}')>"
    
    @property
    def display_name(self) -> str:
        """Get best available name for display"""
        if self.full_name:
            return self.full_name
        elif self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        elif self.phone_number:
            return f"Caller {self.phone_number}"
        elif self.email:
            return self.email.split('@')[0]
        else:
            return f"User {self.id}"
    
    @property
    def contact_info(self) -> str:
        """Get primary contact information"""
        if self.phone_number:
            return self.phone_number
        elif self.email:
            return self.email
        else:
            return "No contact info"
    
    def add_tag(self, tag: str) -> None:
        """Add a tag to the user"""
        if not self.tags:
            self.tags = []
        if tag not in self.tags:
            self.tags.append(tag)
    
    def remove_tag(self, tag: str) -> None:
        """Remove a tag from the user"""
        if self.tags and tag in self.tags:
            self.tags.remove(tag)
    
    def has_tag(self, tag: str) -> bool:
        """Check if user has a specific tag"""
        return self.tags and tag in self.tags
    
    def set_custom_field(self, key: str, value: Any) -> None:
        """Set a custom field value"""
        if not self.custom_fields:
            self.custom_fields = {}
        self.custom_fields[key] = value
    
    def get_custom_field(self, key: str, default: Any = None) -> Any:
        """Get a custom field value"""
        if not self.custom_fields:
            return default
        return self.custom_fields.get(key, default)
    
    def increment_calls(self) -> None:
        """Increment call count and update last call date"""
        self.total_calls = (self.total_calls or 0) + 1
        self.last_call_date = func.now()
    
    def increment_appointments(self) -> None:
        """Increment appointment count"""
        self.total_appointments = (self.total_appointments or 0) + 1
