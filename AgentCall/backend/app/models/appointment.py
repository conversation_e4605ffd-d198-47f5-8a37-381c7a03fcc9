from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey, JSON, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base
from datetime import datetime, timedelta
from typing import Optional, Dict, Any


class Appointment(Base):
    """Model for storing appointment bookings"""
    __tablename__ = "appointments"

    id = Column(Integer, primary_key=True, index=True)
    call_id = Column(Integer, ForeignKey("calls.id"), nullable=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True)
    
    # Appointment details
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    appointment_type = Column(String(100), nullable=True)  # consultation, meeting, follow_up
    
    # Scheduling information
    scheduled_date = Column(DateTime(timezone=True), nullable=False)
    duration_minutes = Column(Integer, default=30)
    end_time = Column(DateTime(timezone=True), nullable=True)
    
    # Status tracking
    status = Column(String(50), default="scheduled")  # scheduled, confirmed, cancelled, completed, no_show
    confirmation_status = Column(String(50), default="pending")  # pending, confirmed, declined
    
    # Contact information (if no user record)
    contact_name = Column(String(255), nullable=True)
    contact_phone = Column(String(50), nullable=True)
    contact_email = Column(String(255), nullable=True)
    
    # Location and method
    location = Column(String(500), nullable=True)
    meeting_type = Column(String(50), default="in_person")  # in_person, phone, video, virtual
    meeting_url = Column(String(500), nullable=True)  # For video calls
    
    # Booking extra data
    booked_by = Column(String(100), default="ai_assistant")  # ai_assistant, staff, self_service
    booking_source = Column(String(100), default="voice_call")  # voice_call, web, mobile_app
    
    # Reminder and notification settings
    send_reminders = Column(Boolean, default=True)
    reminder_method = Column(String(50), default="phone")  # phone, email, sms
    reminder_time_minutes = Column(Integer, default=60)  # Minutes before appointment
    
    # AI context and notes
    ai_booking_context = Column(Text, nullable=True)  # What the AI understood from the conversation
    special_requests = Column(Text, nullable=True)
    preparation_notes = Column(Text, nullable=True)
    
    # Business logic
    requires_preparation = Column(Boolean, default=False)
    estimated_value = Column(Float, nullable=True)  # Potential business value
    priority_level = Column(String(50), default="normal")  # low, normal, high, urgent
    
    # Cancellation and rescheduling
    cancellation_reason = Column(String(200), nullable=True)
    cancelled_by = Column(String(100), nullable=True)
    cancelled_at = Column(DateTime(timezone=True), nullable=True)
    reschedule_count = Column(Integer, default=0)
    original_date = Column(DateTime(timezone=True), nullable=True)
    
    # Follow-up tracking
    follow_up_required = Column(Boolean, default=False)
    follow_up_notes = Column(Text, nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Additional metadata
    extra_data = Column(JSON, nullable=True)  # Flexible additional data
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    confirmed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    call = relationship("Call", back_populates="appointments")
    # user = relationship("User", back_populates="appointments")  # Uncomment when User model is ready

    def __repr__(self):
        return f"<Appointment(id={self.id}, title='{self.title}', date='{self.scheduled_date}', status='{self.status}')>"
    
    @property
    def contact_display(self) -> str:
        """Get best available contact information for display"""
        if self.contact_name:
            return self.contact_name
        elif self.contact_phone:
            return self.contact_phone
        elif self.contact_email:
            return self.contact_email
        else:
            return f"Appointment {self.id}"
    
    @property
    def duration_formatted(self) -> str:
        """Get formatted duration"""
        hours = self.duration_minutes // 60
        minutes = self.duration_minutes % 60
        if hours > 0:
            return f"{hours}h {minutes}m" if minutes > 0 else f"{hours}h"
        return f"{minutes}m"
    
    @property
    def time_until_appointment(self) -> Optional[timedelta]:
        """Get time until appointment"""
        if not self.scheduled_date:
            return None
        now = datetime.utcnow()
        if self.scheduled_date > now:
            return self.scheduled_date - now
        return None
    
    @property
    def is_upcoming(self) -> bool:
        """Check if appointment is upcoming"""
        return self.scheduled_date and self.scheduled_date > datetime.utcnow()
    
    @property
    def is_overdue(self) -> bool:
        """Check if appointment is overdue"""
        return (self.scheduled_date and 
                self.scheduled_date < datetime.utcnow() and 
                self.status in ["scheduled", "confirmed"])
    
    def calculate_end_time(self) -> None:
        """Calculate and set end time based on duration"""
        if self.scheduled_date and self.duration_minutes:
            self.end_time = self.scheduled_date + timedelta(minutes=self.duration_minutes)
    
    def confirm_appointment(self) -> None:
        """Mark appointment as confirmed"""
        self.confirmation_status = "confirmed"
        self.confirmed_at = datetime.utcnow()
        if self.status == "scheduled":
            self.status = "confirmed"
    
    def cancel_appointment(self, reason: str = None, cancelled_by: str = None) -> None:
        """Cancel the appointment"""
        self.status = "cancelled"
        self.cancellation_reason = reason
        self.cancelled_by = cancelled_by
        self.cancelled_at = datetime.utcnow()
    
    def reschedule_appointment(self, new_date: datetime) -> None:
        """Reschedule the appointment"""
        if not self.original_date:
            self.original_date = self.scheduled_date
        self.scheduled_date = new_date
        self.reschedule_count += 1
        self.calculate_end_time()
        self.status = "scheduled"
        self.confirmation_status = "pending"
    
    def mark_completed(self, notes: str = None) -> None:
        """Mark appointment as completed"""
        self.status = "completed"
        self.completed_at = datetime.utcnow()
        if notes:
            self.follow_up_notes = notes
    
    def mark_no_show(self) -> None:
        """Mark appointment as no-show"""
        self.status = "no_show"
    
    def set_extra_data(self, key: str, value: Any) -> None:
        """Set an extra data value"""
        if not self.extra_data:
            self.extra_data = {}
        self.extra_data[key] = value

    def get_extra_data(self, key: str, default: Any = None) -> Any:
        """Get an extra data value"""
        if not self.extra_data:
            return default
        return self.extra_data.get(key, default)
    
    @classmethod
    def create_from_call(cls, call_id: int, title: str, scheduled_date: datetime, 
                        contact_name: str = None, contact_phone: str = None,
                        duration_minutes: int = 30, appointment_type: str = None) -> "Appointment":
        """Create appointment from a call"""
        appointment = cls(
            call_id=call_id,
            title=title,
            scheduled_date=scheduled_date,
            duration_minutes=duration_minutes,
            appointment_type=appointment_type,
            contact_name=contact_name,
            contact_phone=contact_phone,
            booking_source="voice_call",
            booked_by="ai_assistant"
        )
        appointment.calculate_end_time()
        return appointment
