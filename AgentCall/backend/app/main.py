from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager
import uvicorn
import os
from loguru import logger

from app.core.config import settings
from app.core.database import engine, Base
from app.api import calls, webrtc, admin, tts
from app.models import Call, User, Message, Appointment  # Import models to register them


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Handle startup and shutdown events"""
    logger.info("Starting AgentCall API server...")
    
    # Create database tables
    Base.metadata.create_all(bind=engine)
    logger.info("Database tables created")

    # Initialize services
    from app.services.whisper_stt_service import whisper_stt_service
    from app.services.tts_service import tts_service

    # Initialize Whisper STT service
    if not await whisper_stt_service.initialize():
        logger.error("Failed to initialize Whisper STT service")
        raise RuntimeError("Whisper STT service initialization failed")
    logger.info("Whisper STT service initialized successfully")

    # Initialize TTS service
    if not await tts_service.initialize():
        logger.error("Failed to initialize TTS service")
        raise RuntimeError("TTS service initialization failed")

    yield
    
    logger.info("Shutting down AgentCall API server...")


# Create FastAPI app
app = FastAPI(
    title="AgentCall API",
    description="AI-powered call answering system",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],  # React dev servers
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(calls.router, prefix="/api/v1/calls", tags=["calls"])
app.include_router(webrtc.router, prefix="/api/v1/webrtc", tags=["webrtc"])
app.include_router(admin.router, prefix="/api/v1/admin", tags=["admin"])
app.include_router(tts.router, prefix="/api/v1/tts", tags=["tts"])

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "AgentCall API",
        "version": "1.0.0"
    }

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Welcome to AgentCall API",
        "docs": "/docs",
        "health": "/health"
    }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
