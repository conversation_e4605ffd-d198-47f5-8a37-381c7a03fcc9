import asyncio
import json
import uuid
from typing import Dict, Any, Optional
from aiortc import RTCPeerConnection, RTCSessionDescription, RTCIceCandidate, RTCDataChannel
from aiortc.contrib.media import MediaPlayer, MediaRecorder
from aiortc.mediastreams import MediaStreamTrack
from loguru import logger
import av
import numpy as np

from app.core.config import settings


class AudioProcessor:
    """Handles audio processing for WebRTC calls"""
    
    def __init__(self):
        self.sample_rate = settings.SAMPLE_RATE
        self.chunk_size = settings.CHUNK_SIZE
        self.channels = settings.CHANNELS
        
    async def process_audio_chunk(self, audio_data: bytes) -> bytes:
        """
        Process incoming audio chunk
        
        Args:
            audio_data: Raw audio bytes
            
        Returns:
            Processed audio bytes
        """
        try:
            # Convert bytes to numpy array
            audio_array = np.frombuffer(audio_data, dtype=np.int16)
            
            # TODO: Add audio processing here (noise reduction, etc.)
            # For now, just return the original audio
            
            return audio_array.tobytes()
            
        except Exception as e:
            logger.error(f"Error processing audio chunk: {e}")
            return audio_data


class CallSession:
    """Represents an active WebRTC call session"""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.pc = RTCPeerConnection()
        self.audio_processor = AudioProcessor()
        self.audio_track = None
        self.data_channel = None
        self.is_active = False
        self.conversation_history = []
        
        # Set up event handlers
        self.pc.on("connectionstatechange", self._on_connection_state_change)
        self.pc.on("track", self._on_track)
        self.pc.on("datachannel", self._on_data_channel)
        
    async def _on_connection_state_change(self):
        """Handle connection state changes"""
        logger.info(f"Connection state changed to: {self.pc.connectionState}")
        
        if self.pc.connectionState == "connected":
            self.is_active = True
            logger.info(f"Call session {self.session_id} connected")

            # Now that connection is established, start audio processing if we have an audio track
            if self.audio_track:
                logger.info(f"Starting audio processing now that connection is established for session {self.session_id}")
                asyncio.create_task(self._process_audio_stream(self.audio_track))
            else:
                logger.warning(f"No audio track available for session {self.session_id}")
        elif self.pc.connectionState in ["disconnected", "failed", "closed"]:
            self.is_active = False
            logger.info(f"Call session {self.session_id} disconnected")

            # Clean up STT session
            try:
                from app.services.whisper_stt_service import whisper_stt_service
                await whisper_stt_service.close_session(self.session_id)
            except Exception as e:
                logger.error(f"Error closing STT session: {e}")
            
    async def _on_track(self, track):
        """Handle incoming media track"""
        logger.info(f"Track received: {track.kind} for session {self.session_id}")

        if track.kind == "audio":
            self.audio_track = track
            logger.info(f"Audio track stored for session {self.session_id}, waiting for connection")
            # Don't start processing immediately - wait for connection to be established
        else:
            logger.warning(f"Received non-audio track: {track.kind}")
            
    async def _on_data_channel(self, channel):
        """Handle data channel"""
        self.data_channel = channel
        channel.on("message", self._on_data_channel_message)
        
    async def _on_data_channel_message(self, message):
        """Handle data channel messages"""
        try:
            data = json.loads(message)
            logger.info(f"Data channel message: {data}")
            
            # Handle different message types
            if data.get("type") == "text":
                await self._handle_text_message(data.get("content", ""))
                
        except Exception as e:
            logger.error(f"Error handling data channel message: {e}")
            
    async def _handle_text_message(self, text: str):
        """Handle text messages from client"""
        from app.services.llm_service import llm_service
        
        try:
            # Process with LLM
            response = await llm_service.process_query(text, {
                "session_id": self.session_id,
                "conversation_history": self.conversation_history
            })
            
            # Add to conversation history
            self.conversation_history.append({"user": text, "ai": response})
            
            # Send response back via data channel
            if self.data_channel:
                await self.data_channel.send(json.dumps({
                    "type": "response",
                    "content": response,
                    "timestamp": str(asyncio.get_event_loop().time())
                }))
                
        except Exception as e:
            logger.error(f"Error handling text message: {e}")
            
    async def _handle_speech_transcript(self, transcript: str):
        """Handle speech transcript from STT and generate TTS response"""
        try:
            logger.info(f"Speech transcript received: '{transcript}'")

            # Process transcript with LLM
            from app.services.llm_service import llm_service
            from app.services.tts_service import tts_service

            response = await llm_service.process_query(transcript, {
                "session_id": self.session_id,
                "conversation_history": self.conversation_history,
                "source": "voice"
            })

            # Generate TTS audio response
            audio_data = None
            if response and response.strip():
                # Create TTS session if not exists
                if self.session_id not in tts_service.active_sessions:
                    await tts_service.create_session(self.session_id)

                # Generate audio
                audio_data = await tts_service.synthesize_text(self.session_id, response)

            # Add to conversation history
            self.conversation_history.append({
                "user": transcript,
                "ai": response,
                "source": "voice",
                "has_audio": audio_data is not None
            })

            # Send response back via data channel
            if self.data_channel:
                response_data = {
                    "type": "speech_response",
                    "transcript": transcript,
                    "response": response,
                    "timestamp": str(asyncio.get_event_loop().time())
                }

                # Include audio data if available
                if audio_data:
                    import base64
                    response_data["audio_data"] = base64.b64encode(audio_data).decode('utf-8')
                    response_data["has_audio"] = True

                await self.data_channel.send(json.dumps(response_data))

        except Exception as e:
            logger.error(f"Error handling speech transcript: {e}")
            
    async def _process_audio_stream(self, track):
        """Process incoming audio stream"""
        try:
            # Import Whisper STT service
            from app.services.whisper_stt_service import whisper_stt_service as stt_service

            # Create STT session
            await stt_service.create_session(
                self.session_id,
                on_transcript=self._handle_speech_transcript
            )

            logger.info(f"Starting audio processing loop for session {self.session_id}")
            logger.info(f"Track state: {track.readyState if hasattr(track, 'readyState') else 'unknown'}")
            frame_count = 0

            try:
                while self.is_active:  # Check if session is still active
                    try:
                        logger.debug(f"Attempting to receive frame {frame_count + 1} for session {self.session_id}")
                        frame = await track.recv()
                        frame_count += 1
                        logger.debug(f"Successfully received frame {frame_count} for session {self.session_id}")
                    except Exception as recv_error:
                        logger.error(f"Error receiving frame {frame_count + 1}: {recv_error}")
                        if "ended" in str(recv_error).lower() or "closed" in str(recv_error).lower():
                            logger.info(f"Track ended normally for session {self.session_id}")
                            break
                        else:
                            logger.warning(f"Unexpected recv error, continuing: {recv_error}")
                            continue

                    if frame_count % 100 == 0:  # Log every 100 frames
                        logger.debug(f"Received frame {frame_count} for session {self.session_id}")

                    # Convert frame to proper format for STT
                    audio_data = self._convert_webrtc_audio(frame)

                    if audio_data:
                        if frame_count <= 5:  # Log first 5 frames
                            logger.info(f"Converted audio frame {frame_count}: {len(audio_data)} bytes")

                        # Process audio
                        processed_audio = await self.audio_processor.process_audio_chunk(audio_data)

                        if frame_count <= 5:  # Log first 5 frames
                            logger.info(f"Processed audio frame {frame_count}: {len(processed_audio)} bytes")

                        # Send to STT service
                        transcript = await stt_service.process_audio_chunk(self.session_id, processed_audio)

                        if transcript:
                            logger.info(f"Got transcript: '{transcript}'")
                            await self._handle_speech_transcript(transcript)
                    else:
                        if frame_count <= 5:  # Log first 5 frames
                            logger.warning(f"Frame {frame_count}: No audio data after conversion")

            except Exception as loop_error:
                logger.error(f"Audio processing loop error for session {self.session_id}: {loop_error}")

            logger.info(f"Audio processing loop ended for session {self.session_id}, processed {frame_count} frames")

        except Exception as e:
            logger.error(f"Error processing audio stream: {e}")

    def _convert_webrtc_audio(self, frame):
        """Convert WebRTC audio frame to format suitable for STT"""
        try:
            # Get audio data as numpy array
            audio_array = frame.to_ndarray()

            logger.debug(f"Original audio frame: shape={audio_array.shape}, dtype={audio_array.dtype}, sample_rate={frame.sample_rate}")

            # Skip empty frames
            if audio_array.size == 0:
                return None

            # Handle different audio array shapes
            if len(audio_array.shape) == 2:
                # If stereo (channels, samples) or (samples, channels)
                if audio_array.shape[0] > audio_array.shape[1]:
                    # (samples, channels) format - take first channel
                    audio_array = audio_array[:, 0]
                else:
                    # (channels, samples) format - take first channel
                    audio_array = audio_array[0, :]
            elif len(audio_array.shape) > 2:
                # Flatten multi-dimensional arrays
                audio_array = audio_array.flatten()

            # WebRTC typically sends float32 audio, convert to int16 for Vosk
            if audio_array.dtype == np.float32:
                # Ensure values are in [-1, 1] range
                audio_array = np.clip(audio_array, -1.0, 1.0)
                # Convert to int16
                audio_array = (audio_array * 32767).astype(np.int16)
            elif audio_array.dtype != np.int16:
                # Convert other formats to int16
                audio_array = audio_array.astype(np.int16)

            # Resample if needed (WebRTC often uses 48kHz, Vosk expects 16kHz)
            if hasattr(frame, 'sample_rate') and frame.sample_rate != settings.SAMPLE_RATE:
                # Simple downsampling - for production, use proper resampling
                downsample_factor = frame.sample_rate // settings.SAMPLE_RATE
                if downsample_factor > 1:
                    # Ensure we don't lose all the data
                    if len(audio_array) > downsample_factor:
                        audio_array = audio_array[::downsample_factor]
                    else:
                        # If we have very few samples, just keep them
                        logger.warning(f"Audio array too small for downsampling: {len(audio_array)} samples")

            # Ensure we have a reasonable amount of audio data
            if len(audio_array) < 10:
                logger.warning(f"Very small audio array: {len(audio_array)} samples")
                return None

            logger.debug(f"Converted audio: shape={audio_array.shape}, dtype={audio_array.dtype}, size={len(audio_array.tobytes())} bytes")

            return audio_array.tobytes()

        except Exception as e:
            logger.error(f"Error converting WebRTC audio: {e}")
            return None
            
    async def create_answer(self, offer_sdp: str) -> str:
        """Create answer for WebRTC offer"""
        try:
            # Set remote description
            offer = RTCSessionDescription(sdp=offer_sdp, type="offer")
            await self.pc.setRemoteDescription(offer)
            
            # Create data channel for text communication
            self.data_channel = self.pc.createDataChannel("chat")
            
            # Create answer
            answer = await self.pc.createAnswer()
            await self.pc.setLocalDescription(answer)
            
            return self.pc.localDescription.sdp
            
        except Exception as e:
            logger.error(f"Error creating answer: {e}")
            raise
            
    async def add_ice_candidate(self, candidate_data: dict):
        """Add ICE candidate"""
        try:
            # Parse the candidate string to extract required fields
            candidate_str = candidate_data["candidate"]
            sdp_mid = candidate_data.get("sdpMid")
            sdp_mline_index = candidate_data.get("sdpMLineIndex")

            # Parse candidate string (format: "foundation component protocol priority ip port typ type")
            # Example: "2055879696 1 udp 2122194687 ************* 54321 typ host"
            parts = candidate_str.split()
            if len(parts) >= 8:
                # Create RTCIceCandidate with correct parameters
                candidate = RTCIceCandidate(
                    component=int(parts[1]),
                    foundation=parts[0],
                    ip=parts[4],
                    port=int(parts[5]),
                    priority=int(parts[3]),
                    protocol=parts[2],
                    type=parts[7],
                )

                # Set additional properties if available
                if sdp_mid is not None:
                    candidate.sdpMid = sdp_mid
                if sdp_mline_index is not None:
                    candidate.sdpMLineIndex = sdp_mline_index

                await self.pc.addIceCandidate(candidate)
                logger.debug(f"Successfully added ICE candidate: {parts[7]} {parts[4]}:{parts[5]}")
            else:
                logger.warning(f"Invalid candidate format: {candidate_str}")

        except Exception as e:
            logger.error(f"Error adding ICE candidate: {e}")
            
    async def close(self):
        """Close the call session"""
        try:
            # Close STT and TTS sessions
            from app.services.whisper_stt_service import whisper_stt_service as stt_service
            from app.services.tts_service import tts_service

            await stt_service.close_session(self.session_id)
            await tts_service.close_session(self.session_id)

            if self.pc:
                await self.pc.close()
            self.is_active = False
            logger.info(f"Call session {self.session_id} closed")

        except Exception as e:
            logger.error(f"Error closing call session: {e}")


class WebRTCService:
    """Service for managing WebRTC call sessions"""
    
    def __init__(self):
        self.sessions: Dict[str, CallSession] = {}
        
    async def create_session(self, session_id: Optional[str] = None) -> str:
        """Create a new call session"""
        if not session_id:
            session_id = str(uuid.uuid4())
            
        session = CallSession(session_id)
        self.sessions[session_id] = session
        
        logger.info(f"Created new call session: {session_id}")
        return session_id
        
    async def get_session(self, session_id: str) -> Optional[CallSession]:
        """Get an existing call session"""
        return self.sessions.get(session_id)
        
    async def create_answer(self, session_id: str, offer_sdp: str) -> str:
        """Create WebRTC answer for a session"""
        session = await self.get_session(session_id)
        if not session:
            raise ValueError(f"Session {session_id} not found")
            
        return await session.create_answer(offer_sdp)
        
    async def add_ice_candidate(self, session_id: str, candidate_data: dict):
        """Add ICE candidate to a session"""
        session = await self.get_session(session_id)
        if not session:
            raise ValueError(f"Session {session_id} not found")
            
        await session.add_ice_candidate(candidate_data)
        
    async def close_session(self, session_id: str):
        """Close a call session"""
        session = self.sessions.pop(session_id, None)
        if session:
            await session.close()
            
    async def get_session_stats(self, session_id: str) -> Dict[str, Any]:
        """Get session statistics"""
        session = await self.get_session(session_id)
        if not session:
            return {}
            
        return {
            "session_id": session_id,
            "is_active": session.is_active,
            "connection_state": session.pc.connectionState,
            "conversation_length": len(session.conversation_history)
        }
        
    async def list_active_sessions(self) -> list:
        """List all active sessions"""
        active_sessions = []
        for session_id, session in self.sessions.items():
            if session.is_active:
                active_sessions.append({
                    "session_id": session_id,
                    "connection_state": session.pc.connectionState,
                    "conversation_length": len(session.conversation_history)
                })
        return active_sessions


# Global instance
webrtc_service = WebRTCService()
