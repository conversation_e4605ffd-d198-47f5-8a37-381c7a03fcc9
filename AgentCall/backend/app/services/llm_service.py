import google.generativeai as genai
from typing import Dict, Any, Optional
from loguru import logger
from app.core.config import settings


class LLMService:
    """Service for handling LLM interactions with Gemini API"""
    
    def __init__(self):
        """Initialize Gemini API"""
        genai.configure(api_key=settings.GEMINI_API_KEY)
        self.model = genai.GenerativeModel('gemini-1.5-flash')
        
        # System prompt for call handling
        self.system_prompt = """
        You are an AI assistant for a call answering service. Your role is to:
        1. Greet callers professionally and warmly
        2. Understand their queries and provide helpful responses
        3. Handle appointment booking requests
        4. Provide information about services
        5. Transfer calls when necessary
        
        Keep responses:
        - Natural and conversational
        - Concise but informative
        - Professional but friendly
        - Under 100 words for phone calls
        
        If asked about services, mention:
        - Available time slots
        - Pricing information
        - Location details
        - Contact information
        
        For appointment booking:
        - Ask for preferred date and time
        - Confirm contact details
        - Provide confirmation details
        """
    
    async def process_query(self, user_input: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Process user query and generate response
        
        Args:
            user_input: The transcribed speech from the caller
            context: Additional context (call history, user info, etc.)
            
        Returns:
            Generated response text
        """
        try:
            # Prepare the full prompt
            full_prompt = f"{self.system_prompt}\n\nCaller: {user_input}\n\nResponse:"
            
            # Add context if provided
            if context:
                context_str = "\n".join([f"{k}: {v}" for k, v in context.items()])
                full_prompt = f"{self.system_prompt}\n\nContext:\n{context_str}\n\nCaller: {user_input}\n\nResponse:"
            
            # Generate response
            response = self.model.generate_content(full_prompt)
            
            # Clean and return response
            generated_text = response.text.strip()
            logger.info(f"Generated response: {generated_text}")
            
            return generated_text
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Error generating response: {e}")
            
            # Handle specific Gemini API errors
            if "quota" in error_msg.lower() or "429" in error_msg:
                return "I'm currently experiencing high demand. Please try again in a few moments, or I can connect you with a human representative."
            elif "api_key" in error_msg.lower() or "401" in error_msg:
                return "I'm having authentication issues right now. Let me connect you with a human representative."
            else:
                return "I apologize, but I'm having trouble processing your request right now. Please try again or hold for a human representative."
    
    async def analyze_intent(self, user_input: str) -> Dict[str, Any]:
        """
        Analyze user intent from the input
        
        Args:
            user_input: The transcribed speech from the caller
            
        Returns:
            Dictionary with intent analysis
        """
        try:
            intent_prompt = f"""
            Analyze the following caller input and determine the intent. 
            Return a JSON-like response with these fields:
            - intent: main intent (greeting, appointment, inquiry, complaint, etc.)
            - confidence: confidence level (0-1)
            - entities: extracted entities (dates, times, names, etc.)
            - urgency: urgency level (low, medium, high)
            
            Caller input: "{user_input}"
            """
            
            response = self.model.generate_content(intent_prompt)
            
            # For now, return a simple analysis
            # In production, you'd parse the JSON response
            return {
                "intent": "general_inquiry",
                "confidence": 0.8,
                "entities": {},
                "urgency": "medium",
                "raw_analysis": response.text
            }
            
        except Exception as e:
            logger.error(f"Error analyzing intent: {e}")
            return {
                "intent": "unknown",
                "confidence": 0.0,
                "entities": {},
                "urgency": "low",
                "error": str(e)
            }
    
    async def generate_knowledge_base_response(self, query: str) -> str:
        """
        Generate response based on knowledge base
        
        Args:
            query: User query
            
        Returns:
            Knowledge-based response
        """
        # This would typically query a vector database or knowledge base
        # For now, we'll use a simple approach with predefined responses
        
        knowledge_base = {
            "hours": "Our office hours are Monday to Friday, 9 AM to 6 PM, and Saturday 10 AM to 4 PM.",
            "pricing": "Our consultation fees start at $150 for a 30-minute session. We also offer package deals.",
            "location": "We're located at 123 Main Street, Downtown. We have parking available.",
            "services": "We offer consultation, planning, and implementation services across various domains.",
            "appointment": "I can help you schedule an appointment. What date and time works best for you?"
        }
        
        query_lower = query.lower()
        
        # Simple keyword matching
        for key, response in knowledge_base.items():
            if key in query_lower:
                return response
        
        # If no match, use Gemini for general response
        return await self.process_query(query)


# Global instance
llm_service = LLMService()
