import asyncio
import io
import threading
import time
from typing import Optional, Callable, Dict, Any, Union
import numpy as np
import soundfile as sf
from loguru import logger
import tempfile
import os

try:
    import TTS
    from TTS.api import TTS as CoquiTTS
    TTS_AVAILABLE = True
except ImportError:
    TTS_AVAILABLE = False
    logger.warning("Coqui TTS not available. Install with: pip install coqui-tts")

from app.core.config import settings


class CoquiTTSEngine:
    """Coqui TTS-based text-to-speech engine"""
    
    def __init__(self, model_name: str = None, vocoder_name: str = None):
        """
        Initialize Coqui TTS engine
        
        Args:
            model_name: TTS model name/path
            vocoder_name: Vocoder model name/path
        """
        self.model_name = model_name or settings.TTS_MODEL
        self.vocoder_name = vocoder_name or settings.TTS_VOCODER
        self.sample_rate = settings.SAMPLE_RATE
        self.tts = None
        self.is_initialized = False
        self._lock = threading.Lock()
        
    def initialize(self) -> bool:
        """Initialize the TTS model"""
        if not TTS_AVAILABLE:
            logger.error("Coqui TTS is not installed")
            return False
            
        try:
            logger.info(f"Loading TTS model: {self.model_name}")
            
            # Initialize TTS with model
            # Use a simpler model if the configured one isn't available
            try:
                self.tts = CoquiTTS(model_name=self.model_name)
            except Exception as e:
                logger.warning(f"Failed to load configured model {self.model_name}: {e}")
                logger.info("Trying to use default English model...")
                
                # Try common available models
                try:
                    # Try jenny model (fast and good quality)
                    self.tts = CoquiTTS("tts_models/en/ljspeech/tacotron2-DDC")
                except:
                    try:
                        # Fallback to a simpler model
                        self.tts = CoquiTTS("tts_models/en/ljspeech/speedy-speech")
                    except:
                        # Last resort - use any available English model
                        available_models = CoquiTTS.list_models()
                        en_models = [m for m in available_models if m.startswith("tts_models/en/")]
                        if en_models:
                            self.tts = CoquiTTS(en_models[0])
                            logger.info(f"Using fallback model: {en_models[0]}")
                        else:
                            raise Exception("No English TTS models available")
            
            self.is_initialized = True
            logger.info("Coqui TTS engine initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Coqui TTS engine: {e}")
            return False
    
    def synthesize(self, text: str, output_format: str = "wav") -> Optional[bytes]:
        """
        Synthesize speech from text
        
        Args:
            text: Text to convert to speech
            output_format: Output audio format (wav, mp3, etc.)
            
        Returns:
            Audio data as bytes or None if failed
        """
        if not self.is_initialized:
            logger.error("TTS engine not initialized")
            return None
            
        if not text or not text.strip():
            logger.warning("Empty text provided for TTS")
            return None
            
        try:
            with self._lock:
                # Clean the text
                text = text.strip()
                logger.info(f"Synthesizing: '{text[:50]}{'...' if len(text) > 50 else ''}'")
                
                # Create temporary file for audio output
                with tempfile.NamedTemporaryFile(suffix=f".{output_format}", delete=False) as tmp_file:
                    tmp_path = tmp_file.name
                
                try:
                    # Generate speech
                    self.tts.tts_to_file(text=text, file_path=tmp_path)
                    
                    # Read the generated audio file
                    with open(tmp_path, 'rb') as f:
                        audio_data = f.read()
                    
                    logger.info(f"Generated audio: {len(audio_data)} bytes")
                    return audio_data
                    
                finally:
                    # Clean up temporary file
                    try:
                        os.unlink(tmp_path)
                    except:
                        pass
                        
        except Exception as e:
            logger.error(f"Error in speech synthesis: {e}")
            return None
    
    def synthesize_to_array(self, text: str) -> Optional[np.ndarray]:
        """
        Synthesize speech and return as numpy array
        
        Args:
            text: Text to convert to speech
            
        Returns:
            Audio data as numpy array or None if failed
        """
        if not self.is_initialized:
            return None
            
        try:
            with self._lock:
                # Generate speech directly to numpy array
                audio_array = self.tts.tts(text=text)
                
                if audio_array is not None:
                    # Ensure it's a numpy array and normalize
                    audio_array = np.array(audio_array, dtype=np.float32)
                    
                    # Normalize to prevent clipping
                    if np.max(np.abs(audio_array)) > 0:
                        audio_array = audio_array / np.max(np.abs(audio_array)) * 0.95
                    
                    logger.info(f"Generated audio array: shape={audio_array.shape}")
                    return audio_array
                    
        except Exception as e:
            logger.error(f"Error in speech synthesis to array: {e}")
            
        return None


class AudioQueue:
    """Queue for managing TTS audio playback"""
    
    def __init__(self, max_queue_size: int = 10):
        """
        Initialize audio queue
        
        Args:
            max_queue_size: Maximum number of audio clips in queue
        """
        self.queue = []
        self.max_queue_size = max_queue_size
        self.current_audio = None
        self.is_playing = False
        
    def add_audio(self, audio_data: bytes, text: str = "") -> bool:
        """
        Add audio to queue
        
        Args:
            audio_data: Audio bytes to add
            text: Original text for reference
            
        Returns:
            True if added successfully
        """
        if len(self.queue) >= self.max_queue_size:
            logger.warning("Audio queue is full, dropping oldest audio")
            self.queue.pop(0)
            
        self.queue.append({
            'audio_data': audio_data,
            'text': text,
            'timestamp': time.time()
        })
        
        return True
    
    def get_next_audio(self) -> Optional[Dict]:
        """Get next audio from queue"""
        if self.queue:
            return self.queue.pop(0)
        return None
    
    def clear(self):
        """Clear the audio queue"""
        self.queue.clear()
        self.current_audio = None
        self.is_playing = False
    
    def get_queue_size(self) -> int:
        """Get current queue size"""
        return len(self.queue)


class TTSService:
    """Service for handling text-to-speech conversion"""
    
    def __init__(self):
        """Initialize TTS service"""
        self.engine = CoquiTTSEngine()
        self.is_initialized = False
        self.active_sessions: Dict[str, Dict] = {}
        
    async def initialize(self) -> bool:
        """Initialize the TTS service"""
        if self.is_initialized:
            return True
            
        try:
            # Initialize in thread to avoid blocking
            loop = asyncio.get_event_loop()
            success = await loop.run_in_executor(None, self.engine.initialize)
            
            if success:
                self.is_initialized = True
                logger.info("TTS service initialized successfully")
            else:
                logger.error("Failed to initialize TTS service")
                
            return success
            
        except Exception as e:
            logger.error(f"Error initializing TTS service: {e}")
            return False
    
    async def create_session(self, session_id: str, 
                           on_audio_ready: Callable[[bytes, str], None] = None) -> bool:
        """
        Create a new TTS session
        
        Args:
            session_id: Session identifier
            on_audio_ready: Callback for when audio is ready
            
        Returns:
            True if session created successfully
        """
        if not self.is_initialized:
            await self.initialize()
            
        if not self.is_initialized:
            return False
            
        try:
            self.active_sessions[session_id] = {
                'queue': AudioQueue(),
                'on_audio_ready': on_audio_ready,
                'last_activity': time.time(),
                'total_requests': 0,
                'successful_requests': 0
            }
            
            logger.info(f"Created TTS session: {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating TTS session: {e}")
            return False
    
    async def synthesize_text(self, session_id: str, text: str, 
                            priority: int = 0) -> Optional[bytes]:
        """
        Synthesize text to speech for a session
        
        Args:
            session_id: Session identifier
            text: Text to synthesize
            priority: Priority level (higher = more important)
            
        Returns:
            Audio data as bytes if successful
        """
        if session_id not in self.active_sessions:
            logger.warning(f"TTS session not found: {session_id}")
            return None
            
        session = self.active_sessions[session_id]
        session['last_activity'] = time.time()
        session['total_requests'] += 1
        
        try:
            # Synthesize in background thread
            loop = asyncio.get_event_loop()
            audio_data = await loop.run_in_executor(
                None, 
                self.engine.synthesize, 
                text
            )
            
            if audio_data:
                session['successful_requests'] += 1
                
                # Add to session queue
                session['queue'].add_audio(audio_data, text)
                
                # Call callback if provided
                if session['on_audio_ready']:
                    try:
                        session['on_audio_ready'](audio_data, text)
                    except Exception as e:
                        logger.error(f"Error in audio ready callback: {e}")
                
                logger.info(f"TTS synthesized for {session_id}: '{text[:50]}{'...' if len(text) > 50 else ''}'")
                return audio_data
            else:
                logger.error(f"Failed to synthesize text for session {session_id}")
                
        except Exception as e:
            logger.error(f"Error synthesizing text: {e}")
            
        return None
    
    async def synthesize_text_streaming(self, session_id: str, text: str) -> Optional[np.ndarray]:
        """
        Synthesize text and return as numpy array for streaming
        
        Args:
            session_id: Session identifier
            text: Text to synthesize
            
        Returns:
            Audio data as numpy array if successful
        """
        if session_id not in self.active_sessions:
            logger.warning(f"TTS session not found: {session_id}")
            return None
            
        session = self.active_sessions[session_id]
        session['last_activity'] = time.time()
        session['total_requests'] += 1
        
        try:
            # Synthesize in background thread
            loop = asyncio.get_event_loop()
            audio_array = await loop.run_in_executor(
                None, 
                self.engine.synthesize_to_array, 
                text
            )
            
            if audio_array is not None:
                session['successful_requests'] += 1
                logger.info(f"TTS streaming synthesized for {session_id}: '{text[:50]}{'...' if len(text) > 50 else ''}'")
                return audio_array
            else:
                logger.error(f"Failed to synthesize text for streaming in session {session_id}")
                
        except Exception as e:
            logger.error(f"Error synthesizing text for streaming: {e}")
            
        return None
    
    async def get_next_audio(self, session_id: str) -> Optional[Dict]:
        """
        Get next audio from session queue
        
        Args:
            session_id: Session identifier
            
        Returns:
            Audio data dict if available
        """
        if session_id not in self.active_sessions:
            return None
            
        session = self.active_sessions[session_id]
        return session['queue'].get_next_audio()
    
    async def clear_session_queue(self, session_id: str) -> bool:
        """
        Clear audio queue for a session
        
        Args:
            session_id: Session identifier
            
        Returns:
            True if cleared successfully
        """
        if session_id not in self.active_sessions:
            return False
            
        try:
            session = self.active_sessions[session_id]
            session['queue'].clear()
            logger.info(f"Cleared TTS queue for session: {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error clearing session queue: {e}")
            return False
    
    async def close_session(self, session_id: str) -> bool:
        """
        Close a TTS session
        
        Args:
            session_id: Session identifier
            
        Returns:
            True if session closed successfully
        """
        try:
            if session_id in self.active_sessions:
                # Clear any remaining audio
                session = self.active_sessions[session_id]
                session['queue'].clear()
                
                # Remove session
                del self.active_sessions[session_id]
                logger.info(f"Closed TTS session: {session_id}")
                
            return True
            
        except Exception as e:
            logger.error(f"Error closing TTS session: {e}")
            return False
    
    async def get_session_stats(self, session_id: str) -> Dict[str, Any]:
        """Get session statistics"""
        if session_id not in self.active_sessions:
            return {}
            
        session = self.active_sessions[session_id]
        return {
            'session_id': session_id,
            'queue_size': session['queue'].get_queue_size(),
            'last_activity': session['last_activity'],
            'total_requests': session['total_requests'],
            'successful_requests': session['successful_requests'],
            'success_rate': session['successful_requests'] / max(session['total_requests'], 1),
            'is_active': True
        }
    
    async def list_active_sessions(self) -> list:
        """List all active TTS sessions"""
        return [
            {
                'session_id': session_id,
                'queue_size': session['queue'].get_queue_size(),
                'last_activity': session['last_activity'],
                'total_requests': session['total_requests'],
                'successful_requests': session['successful_requests']
            }
            for session_id, session in self.active_sessions.items()
        ]
    
    async def get_available_models(self) -> list:
        """Get list of available TTS models"""
        if not TTS_AVAILABLE:
            return []
            
        try:
            # Get available models from Coqui TTS
            models = CoquiTTS.list_models()
            # Filter to English models for simplicity
            en_models = [m for m in models if '/en/' in m]
            return en_models
        except Exception as e:
            logger.error(f"Error getting available models: {e}")
            return []


# Global instance
tts_service = TTSService()
