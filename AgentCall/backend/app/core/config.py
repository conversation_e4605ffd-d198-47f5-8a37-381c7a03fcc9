from pydantic_settings import BaseSettings
from typing import Optional
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class Settings(BaseSettings):
    """Application settings"""
    
    # Database
    DATABASE_URL: str = "sqlite:///./agentcall.db"
    
    # Google Gemini API
    GEMINI_API_KEY: str = ""
    
    # Security
    SECRET_KEY: str = "default-secret-key-for-development"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Server Configuration
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    DEBUG: bool = True
    
    # Asterisk Configuration
    ASTERISK_HOST: str = "localhost"
    ASTERISK_PORT: int = 5038
    ASTERISK_USERNAME: str = "admin"
    ASTERISK_PASSWORD: str = "secret"
    
    # WebRTC Configuration
    WEBRTC_HOST: str = "localhost"
    WEBRTC_PORT: int = 8088
    STUN_SERVER: str = "stun:stun.l.google.com:19302"
    
    # Audio Configuration
    SAMPLE_RATE: int = 16000
    CHUNK_SIZE: int = 1024
    CHANNELS: int = 1
    
    # TTS Configuration
    TTS_MODEL: str = "tts_models/en/ljspeech/tacotron2-DDC"
    TTS_VOCODER: str = "vocoder_models/en/ljspeech/hifigan_v2"
    
    # STT Configuration (Whisper only)
    WHISPER_MODEL_SIZE: str = "base"  # tiny, base, small, medium, large
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "./logs/agentcall.log"
    
    # Redis (for Celery)
    REDIS_URL: str = "redis://localhost:6379/0"
    
    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "allow"  # Allow extra fields for WebRTC-only mode


# Create settings instance
settings = Settings()
