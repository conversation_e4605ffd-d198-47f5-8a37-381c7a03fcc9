from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from loguru import logger
import base64
import tempfile
import os
from sqlalchemy.orm import Session

from app.services.llm_service import llm_service
from app.services.stt_service import stt_service
from app.services.tts_service import tts_service
from app.core.database import get_db
from app.models.call import Call
from app.models.message import Message

router = APIRouter()


class CallRequest(BaseModel):
    """Request model for call processing"""
    audio_data: str  # Base64 encoded audio
    session_id: str
    context: Dict[str, Any] = {}


class MessageRequest(BaseModel):
    """Request model for text message processing"""
    message: str
    session_id: str
    context: Dict[str, Any] = {}


class CallResponse(BaseModel):
    """Response model for call processing"""
    response_text: str
    audio_data: Optional[str] = None  # Base64 encoded audio
    audio_url: Optional[str] = None
    session_id: str
    intent: Dict[str, Any] = {}
    transcript: Optional[str] = None
    processing_time: Optional[float] = None


@router.post("/process", response_model=CallResponse)
async def process_call(request: CallRequest, db: Session = Depends(get_db)):
    """
    Process incoming call audio and generate complete response with STT->LLM->TTS pipeline
    """
    import time
    start_time = time.time()

    try:
        logger.info(f"Processing call for session: {request.session_id}")

        # Initialize services if needed
        if not stt_service.is_initialized:
            await stt_service.initialize()
        if not tts_service.is_initialized:
            await tts_service.initialize()

        # Decode base64 audio data
        try:
            audio_bytes = base64.b64decode(request.audio_data)
            logger.info(f"Decoded audio: {len(audio_bytes)} bytes")
        except Exception as e:
            logger.error(f"Failed to decode audio data: {e}")
            raise HTTPException(status_code=400, detail="Invalid audio data")

        # Create or get call record
        call = db.query(Call).filter(Call.session_id == request.session_id).first()
        if not call:
            call = Call(session_id=request.session_id, status="active")
            db.add(call)
            db.commit()
            db.refresh(call)

        # Create STT session if not exists
        if request.session_id not in stt_service.active_sessions:
            await stt_service.create_session(request.session_id)

        # Process audio with STT
        transcribed_text = await stt_service.process_audio_chunk(request.session_id, audio_bytes)

        if not transcribed_text:
            # If no speech detected, return empty response
            return CallResponse(
                response_text="",
                session_id=request.session_id,
                transcript="",
                processing_time=time.time() - start_time
            )

        logger.info(f"Transcribed: '{transcribed_text}'")

        # Process with LLM
        response_text = await llm_service.process_query(
            transcribed_text,
            request.context
        )

        # Analyze intent
        intent = await llm_service.analyze_intent(transcribed_text)

        # Create TTS session if not exists
        if request.session_id not in tts_service.active_sessions:
            await tts_service.create_session(request.session_id)

        # Generate audio response with TTS
        audio_data = await tts_service.synthesize_text(request.session_id, response_text)
        audio_data_b64 = None
        if audio_data:
            audio_data_b64 = base64.b64encode(audio_data).decode('utf-8')
            logger.info(f"Generated TTS audio: {len(audio_data)} bytes")

        # Save user message to database
        user_message = Message.create_user_message(
            call_id=call.id,
            content=transcribed_text,
            confidence_score=intent.get("confidence", 0.0)
        )
        user_message.detected_intent = intent.get("intent", "unknown")
        user_message.intent_confidence = intent.get("confidence", 0.0)
        db.add(user_message)

        # Save AI response to database
        ai_message = Message.create_ai_response(
            call_id=call.id,
            content=response_text,
            intent=intent.get("intent", "unknown")
        )
        db.add(ai_message)

        # Update call record
        call.total_messages += 1
        if call.conversation_history is None:
            call.conversation_history = []
        call.conversation_history.append({
            "user": transcribed_text,
            "ai": response_text,
            "timestamp": time.time(),
            "intent": intent
        })

        db.commit()

        processing_time = time.time() - start_time
        logger.info(f"Call processing completed in {processing_time:.2f}s")

        return CallResponse(
            response_text=response_text,
            audio_data=audio_data_b64,
            session_id=request.session_id,
            intent=intent,
            transcript=transcribed_text,
            processing_time=processing_time
        )

    except Exception as e:
        logger.error(f"Error processing call: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to process call: {str(e)}")


@router.post("/message", response_model=CallResponse)
async def process_message(request: MessageRequest, db: Session = Depends(get_db)):
    """
    Process text message (for testing without audio) with TTS response
    """
    import time
    start_time = time.time()

    try:
        logger.info(f"Processing message for session: {request.session_id}")

        # Initialize TTS service if needed
        if not tts_service.is_initialized:
            await tts_service.initialize()

        # Create or get call record
        call = db.query(Call).filter(Call.session_id == request.session_id).first()
        if not call:
            call = Call(session_id=request.session_id, status="active", call_type="text")
            db.add(call)
            db.commit()
            db.refresh(call)

        # Process with LLM
        response_text = await llm_service.process_query(
            request.message,
            request.context
        )

        # Analyze intent
        intent = await llm_service.analyze_intent(request.message)

        # Create TTS session if not exists
        if request.session_id not in tts_service.active_sessions:
            await tts_service.create_session(request.session_id)

        # Generate audio response with TTS
        audio_data = await tts_service.synthesize_text(request.session_id, response_text)
        audio_data_b64 = None
        if audio_data:
            audio_data_b64 = base64.b64encode(audio_data).decode('utf-8')
            logger.info(f"Generated TTS audio: {len(audio_data)} bytes")

        # Save user message to database
        user_message = Message(
            call_id=call.id,
            content=request.message,
            message_type="user_text",
            sender="user",
            detected_intent=intent.get("intent", "unknown"),
            intent_confidence=intent.get("confidence", 0.0)
        )
        db.add(user_message)

        # Save AI response to database
        ai_message = Message.create_ai_response(
            call_id=call.id,
            content=response_text,
            intent=intent.get("intent", "unknown")
        )
        db.add(ai_message)

        # Update call record
        call.total_messages += 1
        if call.conversation_history is None:
            call.conversation_history = []
        call.conversation_history.append({
            "user": request.message,
            "ai": response_text,
            "timestamp": time.time(),
            "intent": intent
        })

        db.commit()

        processing_time = time.time() - start_time
        logger.info(f"Message processing completed in {processing_time:.2f}s")

        return CallResponse(
            response_text=response_text,
            audio_data=audio_data_b64,
            session_id=request.session_id,
            intent=intent,
            transcript=request.message,
            processing_time=processing_time
        )

    except Exception as e:
        logger.error(f"Error processing message: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to process message: {str(e)}")


@router.get("/sessions/{session_id}/history")
async def get_call_history(session_id: str, db: Session = Depends(get_db)):
    """
    Get call history for a session
    """
    try:
        # Get call record
        call = db.query(Call).filter(Call.session_id == session_id).first()
        if not call:
            return {
                "session_id": session_id,
                "call": None,
                "messages": [],
                "total_messages": 0
            }

        # Get all messages for this call
        messages = db.query(Message).filter(Message.call_id == call.id).order_by(Message.created_at).all()

        return {
            "session_id": session_id,
            "call": call.to_dict(),
            "messages": [msg.to_dict() for msg in messages],
            "total_messages": len(messages),
            "conversation_history": call.conversation_history or []
        }

    except Exception as e:
        logger.error(f"Error getting call history: {e}")
        raise HTTPException(status_code=500, detail="Failed to get call history")


@router.post("/sessions/{session_id}/end")
async def end_call_session(session_id: str, db: Session = Depends(get_db)):
    """
    End a call session and cleanup resources
    """
    try:
        # Update call record
        call = db.query(Call).filter(Call.session_id == session_id).first()
        if call:
            call.end_call()
            db.commit()

        # Close service sessions
        await stt_service.close_session(session_id)
        await tts_service.close_session(session_id)

        logger.info(f"Ended call session: {session_id}")
        return {"session_id": session_id, "status": "ended"}

    except Exception as e:
        logger.error(f"Error ending call session: {e}")
        raise HTTPException(status_code=500, detail="Failed to end call session")


@router.get("/sessions")
async def list_active_sessions(db: Session = Depends(get_db)):
    """
    List all active call sessions
    """
    try:
        # Get active calls from database
        active_calls = db.query(Call).filter(Call.status == "active").all()

        # Get service session stats
        stt_sessions = await stt_service.list_active_sessions()
        tts_sessions = await tts_service.list_active_sessions()

        return {
            "active_calls": [call.to_dict() for call in active_calls],
            "stt_sessions": stt_sessions,
            "tts_sessions": tts_sessions,
            "total_active": len(active_calls)
        }

    except Exception as e:
        logger.error(f"Error listing active sessions: {e}")
        raise HTTPException(status_code=500, detail="Failed to list active sessions")


@router.get("/health")
async def health_check():
    """
    Health check for call processing service
    """
    return {
        "status": "healthy",
        "service": "call_processing",
        "llm_service": "gemini" if hasattr(llm_service, 'model') else "unavailable",
        "stt_service": "initialized" if stt_service.is_initialized else "not_initialized",
        "tts_service": "initialized" if tts_service.is_initialized else "not_initialized"
    }
