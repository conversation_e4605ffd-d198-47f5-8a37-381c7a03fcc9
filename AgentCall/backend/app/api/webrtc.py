from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, Optional
from loguru import logger

from app.services.webrtc_service import webrtc_service

router = APIRouter()


class RTCSessionRequest(BaseModel):
    """Request model for WebRTC session"""
    offer: str
    session_id: Optional[str] = None


class RTCIceCandidateRequest(BaseModel):
    """Request model for ICE candidate"""
    candidate: str
    sdpMid: Optional[str] = None
    sdpMLineIndex: Optional[int] = None
    session_id: str


class CreateSessionRequest(BaseModel):
    """Request model for creating new session"""
    session_id: Optional[str] = None


class RTCSessionResponse(BaseModel):
    """Response model for WebRTC session"""
    answer: str
    session_id: str


@router.post("/create-session")
async def create_session(request: CreateSessionRequest):
    """
    Create a new WebRTC session
    """
    try:
        session_id = await webrtc_service.create_session(request.session_id)
        
        return {
            "session_id": session_id,
            "status": "created"
        }
        
    except Exception as e:
        logger.error(f"Error creating session: {e}")
        raise HTTPException(status_code=500, detail="Failed to create session")


@router.post("/session/offer", response_model=RTCSessionResponse)
async def handle_webrtc_offer(request: RTCSessionRequest):
    """
    Handle WebRTC offer and return answer
    """
    try:
        # Create session if not provided
        if not request.session_id:
            session_id = await webrtc_service.create_session()
        else:
            session_id = request.session_id
            
        logger.info(f"Handling WebRTC offer for session: {session_id}")
        
        # Create answer
        answer_sdp = await webrtc_service.create_answer(session_id, request.offer)
        
        return RTCSessionResponse(
            answer=answer_sdp,
            session_id=session_id
        )
        
    except Exception as e:
        logger.error(f"Error handling WebRTC offer: {e}")
        raise HTTPException(status_code=500, detail="Failed to handle WebRTC offer")


@router.get("/session/{session_id}/status")
async def get_session_status(session_id: str):
    """
    Get WebRTC session status
    """
    try:
        session_stats = await webrtc_service.get_session_stats(session_id)
        return session_stats
        
    except Exception as e:
        logger.error(f"Error getting session status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get session status")


@router.post("/session/ice-candidate")
async def add_ice_candidate(request: RTCIceCandidateRequest):
    """
    Add ICE candidate to WebRTC session
    """
    try:
        logger.debug(f"Received ICE candidate request: session_id={request.session_id}, candidate={request.candidate[:50] if request.candidate else 'None'}...")
        
        # Validate required fields
        if not request.session_id:
            logger.warning("ICE candidate request missing session_id")
            raise HTTPException(status_code=422, detail="session_id is required")
        
        if not request.candidate or request.candidate.strip() == "":
            logger.debug(f"Empty ICE candidate for session {request.session_id}, ignoring")
            # Return success for empty candidates to avoid client-side errors
            return {
                "status": "success",
                "message": "Empty candidate ignored"
            }
        
        # Check if session exists
        session = await webrtc_service.get_session(request.session_id)
        if not session:
            logger.error(f"Session {request.session_id} not found for ICE candidate")
            raise HTTPException(status_code=422, detail=f"Session {request.session_id} not found")
        
        candidate_data = {
            "candidate": request.candidate,
            "sdpMid": request.sdpMid,
            "sdpMLineIndex": request.sdpMLineIndex
        }
        
        await webrtc_service.add_ice_candidate(request.session_id, candidate_data)
        logger.debug(f"ICE candidate added successfully for session {request.session_id}")
        
        return {
            "status": "success",
            "message": "ICE candidate added"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding ICE candidate for session {request.session_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to add ICE candidate: {str(e)}")


@router.delete("/session/{session_id}")
async def close_webrtc_session(session_id: str):
    """
    Close WebRTC session
    """
    try:
        logger.info(f"Closing WebRTC session: {session_id}")
        
        await webrtc_service.close_session(session_id)
        
        return {
            "session_id": session_id,
            "status": "closed"
        }
        
    except Exception as e:
        logger.error(f"Error closing WebRTC session: {e}")
        raise HTTPException(status_code=500, detail="Failed to close session")


@router.get("/health")
async def health_check():
    """
    Health check for WebRTC service
    """
    return {
        "status": "healthy",
        "service": "webrtc",
        "active_sessions": 0  # TODO: Implement actual session count
    }
