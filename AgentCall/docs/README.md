# AgentCall Documentation

This directory contains comprehensive documentation for the AgentCall AI call answering system.

## 📁 Files Overview

### 📊 System Diagrams
- **`system-diagrams.md`** - Complete system architecture and workflow diagrams in Mermaid format
- **`diagrams-viewer.html`** - Interactive HTML viewer for all system diagrams with SVG rendering

### 📖 How to View the Diagrams

#### Option 1: HTML Viewer (Recommended)
1. Open `diagrams-viewer.html` in any modern web browser
2. All diagrams will render automatically with interactive features
3. Includes color-coded implementation status and navigation

#### Option 2: Markdown with Mermaid Support
1. **GitHub/GitLab**: View `system-diagrams.md` directly (auto-renders Mermaid)
2. **VS Code**: Install "Markdown Preview Mermaid Support" extension
3. **Online**: Copy diagram code to [Mermaid Live Editor](https://mermaid.live/)

#### Option 3: Export as Images
1. Open `diagrams-viewer.html` in browser
2. Right-click on any diagram → "Save image as..." to export as PNG/SVG
3. Use for presentations or documentation

## 🎯 Diagram Types Included

### 1. System Workflow Overview
- Complete call flow from incoming call to response
- Shows all processing stages and decision points
- Color-coded implementation status

### 2. Technical Architecture
- Infrastructure components and services
- Network topology and port configurations
- Service dependencies and relationships

### 3. Call Processing Sequence
- Step-by-step call handling process
- Real-time audio processing loop
- Database interactions and logging

### 4. Data Flow Architecture
- Information flow through the system
- Processing stages and transformations
- Storage and caching mechanisms

## 🎨 Color Coding Legend

- 🟢 **Green**: Fully implemented and functional
- 🔴 **Red**: Not implemented, needs development
- 🟡 **Orange**: Partially implemented, needs completion
- 🟣 **Purple**: External services and dependencies

## 🔧 Technical Specifications

### Audio Processing
- **Sample Rate**: 16kHz
- **Channels**: Mono (1 channel)
- **Format**: WAV/PCM
- **Latency**: <2 seconds end-to-end

### Network Configuration
- **WebRTC**: Ports 5088, 5089
- **SIP**: Port 5060 (UDP)
- **RTP**: Ports 10000-20000 (UDP)
- **HTTP/HTTPS**: Ports 80, 443

### System Requirements
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 50GB for models and audio files
- **CPU**: Multi-core for real-time processing
- **Network**: Public IP or port forwarding

## 📋 Implementation Roadmap

### Phase 1: Core Services (Weeks 1-2)
- [ ] STT Service (Vosk)
- [ ] TTS Service (Coqui TTS)
- [ ] Database Models
- [ ] Audio Processing

### Phase 2: Communication (Weeks 3-4)
- [ ] WebRTC Implementation
- [ ] Asterisk Configuration
- [ ] Session Management
- [ ] Call Routing

### Phase 3: Frontend (Weeks 5-6)
- [ ] React Components
- [ ] Admin Dashboard
- [ ] Real-time Interface
- [ ] Call Monitoring

### Phase 4: Integration (Weeks 7-8)
- [ ] End-to-end Testing
- [ ] Performance Optimization
- [ ] Security Hardening
- [ ] Production Deployment

## 🔗 Related Files

- [`../README.md`](../README.md) - Main project documentation
- [`../backend/app/core/config.py`](../backend/app/core/config.py) - Configuration settings
- [`../docker-compose.yml`](../docker-compose.yml) - Development environment
- [`../prompt.md`](../prompt.md) - Original project requirements

## 🛠 Updating Diagrams

To modify or add new diagrams:

1. **Edit Mermaid Code**: Update diagram definitions in `system-diagrams.md`
2. **Update HTML Viewer**: Add new diagrams to `diagrams-viewer.html`
3. **Test Rendering**: Verify diagrams render correctly in both formats
4. **Update Documentation**: Add descriptions and context for new diagrams

## 📞 Support

For questions about the system architecture or diagrams:
- Review the main [README.md](../README.md)
- Check the [API documentation](http://localhost:8000/docs) when running
- Open an issue for clarifications or improvements

---

*Last updated: 2024-01-01*
*AgentCall v1.0.0*
