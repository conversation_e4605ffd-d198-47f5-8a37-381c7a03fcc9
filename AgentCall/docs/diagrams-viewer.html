<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AgentCall System Diagrams</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 40px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        .diagram-container {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .mermaid {
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .legend {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .legend h3 {
            margin-top: 0;
            color: #2980b9;
        }
        .status-item {
            display: inline-block;
            margin: 5px 10px;
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        .implemented { background: #90EE90; color: #006400; }
        .missing { background: #FFB6C1; color: #DC143C; }
        .partial { background: #FFE4B5; color: #FF8C00; }
        .external { background: #E6E6FA; color: #4B0082; }
        .toc {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 8px 0;
        }
        .toc a {
            color: #3498db;
            text-decoration: none;
            font-weight: 500;
        }
        .toc a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AgentCall System Architecture & Workflow Diagrams</h1>
        
        <div class="legend">
            <h3>📊 Implementation Status Legend</h3>
            <span class="status-item implemented">✅ Implemented</span>
            <span class="status-item missing">❌ Missing</span>
            <span class="status-item partial">🟡 Partial</span>
            <span class="status-item external">🟣 External</span>
        </div>

        <div class="toc">
            <h3>📋 Table of Contents</h3>
            <ul>
                <li><a href="#workflow">1. System Workflow Overview</a></li>
                <li><a href="#architecture">2. Technical Architecture</a></li>
                <li><a href="#sequence">3. Call Processing Sequence</a></li>
                <li><a href="#dataflow">4. Data Flow Architecture</a></li>
            </ul>
        </div>

        <div id="workflow" class="diagram-container">
            <h2>1. System Workflow Overview</h2>
            <p>This diagram shows the complete call flow from incoming call to response delivery, highlighting the current implementation status.</p>
            <div class="mermaid">
graph TD
    A[Incoming Call] --> B{Call Type}
    B -->|WebRTC Browser| C[WebRTC Client]
    B -->|SIP Phone| D[Asterisk PBX]
    
    C --> E[WebRTC Session Creation]
    D --> F[SIP to WebRTC Bridge]
    F --> E
    
    E --> G[Audio Stream Established]
    G --> H[Real-time Audio Processing]
    
    H --> I[STT Service<br/>Vosk]
    I --> J[Text Transcription]
    
    J --> K[LLM Service<br/>Gemini API]
    K --> L[Intent Analysis]
    K --> M[Response Generation]
    
    M --> N[Knowledge Base<br/>Query]
    N --> O[Contextual Response]
    
    O --> P[TTS Service<br/>Coqui TTS]
    P --> Q[Audio Response]
    
    Q --> R[Audio Stream Back]
    R --> S[Caller Hears Response]
    
    %% Database Operations
    J --> T[(Call Logs<br/>SQLite)]
    L --> T
    O --> T
    
    %% Admin Interface
    U[Admin Dashboard] --> V[Call Monitoring]
    V --> T
    U --> W[System Configuration]
    U --> X[Appointment Management]
    
    %% Current Implementation Status
    classDef implemented fill:#90EE90,stroke:#006400,stroke-width:2px
    classDef missing fill:#FFB6C1,stroke:#DC143C,stroke-width:2px
    classDef partial fill:#FFE4B5,stroke:#FF8C00,stroke-width:2px
    
    %% Mark implemented components
    class K,L,M,N,O implemented
    
    %% Mark missing components
    class C,D,E,F,G,H,I,J,P,Q,R,S,T,U,V,W,X missing
    
    %% Mark partial components
    class A,B partial
            </div>
        </div>

        <div id="architecture" class="diagram-container">
            <h2>2. Technical Architecture</h2>
            <p>This diagram illustrates the complete system architecture including all services, infrastructure components, and their relationships.</p>
            <div class="mermaid">
graph TB
    subgraph "Frontend Layer"
        WEB[React Web App<br/>Port 3000]
        WEBRTC[WebRTC Client<br/>Browser API]
    end
    
    subgraph "API Gateway"
        NGINX[Nginx Reverse Proxy<br/>Port 80/443]
    end
    
    subgraph "Backend Services"
        API[FastAPI Server<br/>Port 8000]
        
        subgraph "Core Services"
            STT[STT Service<br/>Vosk Engine]
            LLM[LLM Service<br/>Gemini API]
            TTS[TTS Service<br/>Coqui TTS]
            CALL[Call Service<br/>Session Management]
        end
        
        subgraph "Communication"
            WEBRTC_SRV[WebRTC Service<br/>aiortc]
            AST_SRV[Asterisk Service<br/>PBX Integration]
        end
    end
    
    subgraph "Telephony Infrastructure"
        AST[Asterisk PBX<br/>Port 5060, 5088]
        SIP[SIP Endpoints]
        WEBRTC_EP[WebRTC Endpoints]
    end
    
    subgraph "Data Layer"
        DB[(SQLite Database<br/>Call Logs, Users)]
        REDIS[(Redis Cache<br/>Sessions, Queue)]
        FILES[File Storage<br/>Audio Files, Models]
    end
    
    subgraph "External Services"
        GEMINI[Google Gemini API<br/>LLM Processing]
        STUN[STUN Server<br/>NAT Traversal]
    end
    
    %% Connections
    WEB --> NGINX
    WEBRTC --> WEBRTC_EP
    NGINX --> API
    
    API --> STT
    API --> LLM
    API --> TTS
    API --> CALL
    API --> WEBRTC_SRV
    API --> AST_SRV
    
    WEBRTC_SRV --> WEBRTC_EP
    AST_SRV --> AST
    AST --> SIP
    AST --> WEBRTC_EP
    
    STT --> FILES
    TTS --> FILES
    CALL --> DB
    CALL --> REDIS
    
    LLM --> GEMINI
    WEBRTC_SRV --> STUN
    
    %% Implementation Status
    classDef implemented fill:#90EE90,stroke:#006400,stroke-width:2px
    classDef missing fill:#FFB6C1,stroke:#DC143C,stroke-width:2px
    classDef partial fill:#FFE4B5,stroke:#FF8C00,stroke-width:2px
    classDef external fill:#E6E6FA,stroke:#4B0082,stroke-width:2px
    
    %% Implemented
    class API,LLM,NGINX,REDIS implemented
    
    %% Missing
    class WEB,WEBRTC,STT,TTS,CALL,WEBRTC_SRV,AST_SRV,AST,SIP,WEBRTC_EP,DB,FILES missing
    
    %% External
    class GEMINI,STUN external
            </div>
        </div>

        <div id="sequence" class="diagram-container">
            <h2>3. Call Processing Sequence</h2>
            <p>This sequence diagram shows the detailed step-by-step process of handling a call from initiation to completion.</p>
            <div class="mermaid">
sequenceDiagram
    participant Caller
    participant WebRTC as WebRTC Client
    participant API as FastAPI Server
    participant STT as STT Service (Vosk)
    participant LLM as LLM Service (Gemini)
    participant TTS as TTS Service (Coqui)
    participant DB as Database
    participant Asterisk as Asterisk PBX
    
    Note over Caller, Asterisk: Call Initiation Phase
    Caller->>WebRTC: Initiate Call (Browser)
    WebRTC->>API: POST /api/v1/webrtc/session
    API->>Asterisk: Create SIP Session
    Asterisk-->>API: Session Created
    API-->>WebRTC: WebRTC Answer
    WebRTC-->>Caller: Audio Connection Established
    
    Note over Caller, Asterisk: Audio Processing Loop
    loop Real-time Audio Processing
        Caller->>WebRTC: Speak (Audio Stream)
        WebRTC->>API: Audio Chunks (Base64)
        API->>STT: Transcribe Audio
        STT-->>API: Text Transcription
        
        API->>DB: Log Transcription
        API->>LLM: Process Query + Context
        LLM->>LLM: Analyze Intent
        LLM->>LLM: Generate Response
        LLM-->>API: Response Text + Intent
        
        API->>TTS: Convert Text to Speech
        TTS-->>API: Audio Response (WAV)
        API->>DB: Log Response
        
        API-->>WebRTC: Audio Response
        WebRTC-->>Caller: Play Response
    end
    
    Note over Caller, Asterisk: Call Termination
    Caller->>WebRTC: End Call
    WebRTC->>API: DELETE /api/v1/webrtc/session/{id}
    API->>DB: Update Call Log (End Time)
    API->>Asterisk: Terminate SIP Session
    API-->>WebRTC: Session Closed
    
    Note over Caller, Asterisk: Current Implementation Status
    Note over STT: ❌ NOT IMPLEMENTED
    Note over TTS: ❌ NOT IMPLEMENTED  
    Note over WebRTC: ❌ PLACEHOLDER ONLY
    Note over Asterisk: ❌ NOT CONFIGURED
    Note over DB: ❌ NO MODELS
    Note over LLM: ✅ IMPLEMENTED
            </div>
        </div>

        <div id="dataflow" class="diagram-container">
            <h2>4. Data Flow Architecture</h2>
            <p>This diagram shows how data flows through the system from input to output, including all processing stages and storage components.</p>
            <div class="mermaid">
graph LR
    subgraph "Input Layer"
        A1[Phone Call<br/>Audio Stream]
        A2[WebRTC<br/>Browser Call]
        A3[SIP Phone<br/>Traditional Call]
    end

    subgraph "Audio Processing"
        B1[Audio Capture<br/>16kHz, Mono]
        B2[Audio Chunking<br/>1024 samples]
        B3[Format Conversion<br/>WAV/PCM]
    end

    subgraph "Speech Recognition"
        C1[Vosk STT Engine<br/>Real-time Processing]
        C2[Language Model<br/>English US]
        C3[Confidence Scoring<br/>Accuracy Check]
    end

    subgraph "Natural Language Processing"
        D1[Text Preprocessing<br/>Cleanup & Normalize]
        D2[Intent Classification<br/>Gemini Analysis]
        D3[Entity Extraction<br/>Names, Dates, Times]
        D4[Context Management<br/>Conversation History]
    end

    subgraph "Knowledge & Response"
        E1[Knowledge Base<br/>FAQ, Services, Hours]
        E2[Response Generation<br/>Gemini LLM]
        E3[Response Validation<br/>Length & Content Check]
        E4[Personalization<br/>Caller Context]
    end

    subgraph "Speech Synthesis"
        F1[Text Processing<br/>SSML Markup]
        F2[Voice Selection<br/>Default/Cloned Voice]
        F3[Coqui TTS Engine<br/>Neural Synthesis]
        F4[Audio Post-processing<br/>Normalization]
    end

    subgraph "Output Layer"
        G1[Audio Response<br/>WAV Stream]
        G2[WebRTC Delivery<br/>Real-time Audio]
        G3[Call Logging<br/>Database Storage]
    end

    subgraph "Data Storage"
        H1[(Call Records<br/>SQLite)]
        H2[(Audio Files<br/>File System)]
        H3[(Session Cache<br/>Redis)]
        H4[(Configuration<br/>Settings)]
    end

    %% Data Flow Connections
    A1 --> B1
    A2 --> B1
    A3 --> B1

    B1 --> B2
    B2 --> B3
    B3 --> C1

    C1 --> C2
    C2 --> C3
    C3 --> D1

    D1 --> D2
    D2 --> D3
    D3 --> D4
    D4 --> E1

    E1 --> E2
    E2 --> E3
    E3 --> E4
    E4 --> F1

    F1 --> F2
    F2 --> F3
    F3 --> F4
    F4 --> G1

    G1 --> G2
    G2 --> G3

    %% Database Connections
    D4 --> H1
    E4 --> H1
    G3 --> H1
    F4 --> H2
    D4 --> H3
    E2 --> H4

    %% Implementation Status Colors
    classDef implemented fill:#90EE90,stroke:#006400,stroke-width:2px
    classDef missing fill:#FFB6C1,stroke:#DC143C,stroke-width:2px
    classDef partial fill:#FFE4B5,stroke:#FF8C00,stroke-width:2px

    %% Mark implementation status
    class D2,E2,E3 implemented
    class A1,A2,A3,B1,B2,B3,C1,C2,C3,D1,D3,D4,E1,E4,F1,F2,F3,F4,G1,G2,G3,H1,H2,H3,H4 missing
            </div>
        </div>

        <div class="legend">
            <h3>📈 Key Metrics & Requirements</h3>
            <ul>
                <li><strong>Response Time:</strong> &lt;2 seconds end-to-end</li>
                <li><strong>Audio Quality:</strong> 16kHz sample rate, mono channel</li>
                <li><strong>Concurrent Calls:</strong> Multiple simultaneous sessions</li>
                <li><strong>Resource Usage:</strong> ~2GB RAM for full stack</li>
                <li><strong>Network Ports:</strong> 5060 (SIP), 5088 (WebRTC), 10000-20000 (RTP)</li>
            </ul>
        </div>

        <div class="legend">
            <h3>🚀 Next Implementation Steps</h3>
            <ol>
                <li><strong>Phase 1:</strong> Implement STT and TTS services</li>
                <li><strong>Phase 2:</strong> Create database models and WebRTC handling</li>
                <li><strong>Phase 3:</strong> Build frontend components and admin panel</li>
                <li><strong>Phase 4:</strong> Configure Asterisk and full integration testing</li>
            </ol>
        </div>
    </div>

    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#3498db',
                primaryTextColor: '#2c3e50',
                primaryBorderColor: '#2980b9',
                lineColor: '#34495e',
                secondaryColor: '#ecf0f1',
                tertiaryColor: '#f8f9fa'
            }
        });
    </script>
</body>
</html>
