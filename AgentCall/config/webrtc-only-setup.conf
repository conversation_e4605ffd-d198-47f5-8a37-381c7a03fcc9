# AgentCall WebRTC-Only Configuration
# No SIP providers, no phone numbers - just web-based AI calling!

# ===== DEPLOYMENT MODE =====
DEPLOYMENT_MODE=webrtc-only
PHONE_INTEGRATION=disabled
SIP_PROVIDERS=disabled

# ===== WEBRTC CONFIGURATION =====
# Your website domain (where the call button will be)
WEBSITE_DOMAIN=localhost:3000
WEBRTC_HOST=0.0.0.0
WEBRTC_PORT=8088

# STUN servers for WebRTC (free Google STUN servers)
STUN_SERVERS=stun:stun.l.google.com:19302,stun:stun1.l.google.com:19302

# ===== NETWORK SETTINGS =====
# Only these ports need to be accessible:
# - 3000 (Frontend website)
# - 8000 (Backend API)
# - 8088 (WebRTC signaling)
# - 10000-10100 (RTP for WebRTC - much smaller range needed)

# Your public IP (for WebRTC ICE candidates)
PUBLIC_IP=auto-detect

# ===== AI AGENT CONFIGURATION =====
AGENT_NAME=AgentCall AI Assistant
AGENT_GREETING=Hello! I'm your AI assistant. How can I help you today?
AGENT_LANGUAGE=en-US
AGENT_VOICE=female
RESPONSE_TIMEOUT=30

# ===== BUSINESS CONTEXT =====
BUSINESS_NAME=Your Business Name
BUSINESS_TYPE=AI Assistant Service
BUSINESS_HOURS=24/7
TIMEZONE=auto-detect

# ===== AUDIO SETTINGS =====
# Optimized for web browsers
AUDIO_CODEC=opus,ulaw,alaw
SAMPLE_RATE=16000
BITRATE=64000
ECHO_CANCELLATION=enabled
NOISE_SUPPRESSION=enabled

# ===== FEATURES ENABLED =====
VOICE_CALLING=enabled
TEXT_CHAT=enabled
CALL_RECORDING=enabled
CONVERSATION_HISTORY=enabled
ANALYTICS=enabled

# ===== FEATURES DISABLED =====
TRADITIONAL_PHONE=disabled
SIP_TRUNKS=disabled
ASTERISK_PBX=disabled
PHONE_NUMBERS=disabled

# ===== SECURITY =====
CORS_ORIGINS=http://localhost:3000,https://yourdomain.com
API_RATE_LIMITING=enabled
SESSION_TIMEOUT=3600

# ===== COST ANALYSIS =====
# Monthly costs with this approach:
# - Server hosting: $5-20/month (VPS)
# - Google Gemini API: $0-10/month (generous free tier)
# - Domain name: $10/year
# - SSL certificate: Free (Let's Encrypt)
# Total: ~$5-30/month vs $50-200/month with SIP providers!

# ===== DEPLOYMENT BENEFITS =====
# ✅ No SIP provider fees
# ✅ No phone number rental costs
# ✅ Easier setup and maintenance
# ✅ Better audio quality (WebRTC)
# ✅ More control over user experience
# ✅ Built-in screen sharing capabilities
# ✅ Easy to scale and customize
# ✅ Works on any device with a browser

# ===== USE CASES =====
# Perfect for:
# - Customer support on websites
# - AI assistants for businesses
# - Virtual consultations
# - Interactive demos
# - Educational platforms
# - E-commerce assistance

# ===== INTEGRATION OPTIONS =====
# Can be embedded in:
# - Your main website
# - Customer support pages
# - Mobile apps (WebView)
# - Kiosks and terminals
# - Social media platforms
# - Email signatures (link to call page)
