# AgentCall Configuration for Indian Number +91 **********
# This file contains specific configuration for deploying AgentCall with Indian telecom providers

# ===== NETWORK CONFIGURATION =====
# Replace these with your actual values

# Your public IP address (get from: curl ifconfig.me)
PUBLIC_IP=YOUR_PUBLIC_IP_HERE

# Your domain name or DDNS (optional but recommended)
DOMAIN=your-domain.com

# Port forwarding requirements for Indian deployment
# Configure these ports on your router/firewall:
# - 5060 (SIP signaling)
# - 5088 (Asterisk HTTP)
# - 8000 (AgentCall API)
# - 8088 (WebRTC)
# - 10000-20000 (RTP audio)

# ===== INDIAN TELECOM PROVIDERS =====
# Choose one and configure based on your provider

# Airtel Configuration
AIRTEL_SIP_HOST=sip.airtel.in
AIRTEL_USERNAME=**********
AIRTEL_PASSWORD=YOUR_AIRTEL_PASSWORD

# Jio Configuration  
JIO_SIP_HOST=sip.jio.com
JIO_USERNAME=**********
JIO_PASSWORD=YOUR_JIO_PASSWORD

# BSNL Configuration
BSNL_SIP_HOST=sip.bsnl.in
BSNL_USERNAME=**********
BSNL_PASSWORD=YOUR_BSNL_PASSWORD

# Third-party VoIP providers (recommended for easier setup)
# Examples: Knowlarity, Exotel, Ozonetel, etc.
VOIP_PROVIDER_HOST=sip.yourprovider.com
VOIP_PROVIDER_USERNAME=**********
VOIP_PROVIDER_PASSWORD=YOUR_PROVIDER_PASSWORD

# ===== AGENTCALL SPECIFIC SETTINGS =====
AGENTCALL_NUMBER=**********
COUNTRY_CODE=91
TIMEZONE=Asia/Kolkata
LANGUAGE=en-IN
CURRENCY=INR

# ===== AUDIO SETTINGS FOR INDIA =====
# Optimized for Indian network conditions
AUDIO_CODEC_PRIORITY=ulaw,alaw,g729
SAMPLE_RATE=8000
BITRATE=64000
JITTER_BUFFER=adaptive

# ===== BUSINESS CONTEXT =====
# Customize these for your specific use case
BUSINESS_NAME=AgentCall India
BUSINESS_HOURS=09:00-18:00 IST
BUSINESS_DAYS=Monday-Saturday
GREETING_MESSAGE=Namaste! Welcome to AgentCall India. How may I assist you today?

# ===== DEPLOYMENT NOTES =====
# 1. Get a VoIP service provider account (recommended: Knowlarity, Exotel)
# 2. Configure your router for port forwarding
# 3. Update the SIP credentials in asterisk/sip.conf
# 4. Test with a local SIP client first
# 5. Configure your domain/DDNS for external access

# ===== TROUBLESHOOTING =====
# Common issues and solutions:
# 
# 1. "Registration failed" - Check SIP credentials and network connectivity
# 2. "No audio" - Verify RTP port range (10000-20000) is open
# 3. "Call drops" - Check NAT settings and external IP configuration
# 4. "Cannot receive calls" - Verify DID routing with your provider
# 5. "Poor audio quality" - Adjust codec settings and check bandwidth

# ===== SECURITY RECOMMENDATIONS =====
# 1. Use strong passwords for SIP accounts
# 2. Enable fail2ban for SIP brute force protection
# 3. Use firewall rules to restrict access
# 4. Regular security updates
# 5. Monitor call logs for suspicious activity

# ===== PERFORMANCE OPTIMIZATION =====
# For Indian network conditions:
# - Use G.729 codec for low bandwidth
# - Enable adaptive jitter buffer
# - Set appropriate timeout values
# - Monitor latency and packet loss

# ===== LEGAL COMPLIANCE =====
# Ensure compliance with Indian telecom regulations:
# - TRAI guidelines for VoIP services
# - Data protection and privacy laws
# - Call recording consent (if applicable)
# - Emergency services routing requirements
