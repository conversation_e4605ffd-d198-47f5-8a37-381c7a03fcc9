nohup: ignoring input
(node:38293) [MONGODB DRIVER] Warning: useNewUrlParser is a deprecated option: useNewUrlParser has no effect since Node.js Driver version 4.0.0 and will be removed in the next major version
(Use `node --trace-warnings ...` to show where the warning was created)
(node:38293) [MONGODB DRIVER] Warning: useUnifiedTopology is a deprecated option: useUnifiedTopology has no effect since Node.js Driver version 4.0.0 and will be removed in the next major version
Server running on port 5000
Available routes:
- POST /api/auth/login
- POST /api/auth/signup
- GET /api/test
- GET /api/patient/dashboard
- GET /api/doctor/dashboard
- GET /api/admin/dashboard
- POST /api/appointments/book
- GET /api/appointments/patient
- GET /api/appointments/doctor
- PUT /api/appointments/update-status/:id
- POST /api/prescriptions/add
- GET /api/prescriptions/patient
- GET /api/prescriptions/doctor
- POST /api/ai/symptom
MongoDB Atlas Connected: cluster0-shard-00-02.lsdff.mongodb.net
✅ Security logs database cleaned of all dummy data
2025-06-24T10:54:24.428Z - GET /api/notifications
Auth check for GET /api/notifications
Token provided: eyJhbGciOiJIUzI...
Token decoded for user: 6826919b29724a087b7be153, role: admin
User authenticated: Admin User, role: admin
2025-06-24T10:54:24.984Z - GET /api/notifications
Auth check for GET /api/notifications
Token provided: eyJhbGciOiJIUzI...
Token decoded for user: 6826919b29724a087b7be153, role: admin
User authenticated: Admin User, role: admin
