import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AdminLayout } from './components/layout/AdminLayout';
import { DashboardOverview } from './pages/DashboardOverview';
import { UserManagement } from './pages/UserManagement';
import { DoctorApprovals } from './pages/DoctorApprovals';
import { AppointmentMonitoring } from './pages/AppointmentMonitoring';
import { PrescriptionLogs } from './pages/PrescriptionLogs';
import { AIAnalytics } from './pages/AIAnalytics';
import { SystemSettings } from './pages/SystemSettings';
import { SecurityLogs } from './pages/SecurityLogs';

function App() {
  return (
    <Router>
      <AdminLayout>
        <Routes>
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          <Route path="/dashboard" element={<DashboardOverview />} />
          <Route path="/users" element={<UserManagement />} />
          <Route path="/doctors" element={<DoctorApprovals />} />
          <Route path="/appointments" element={<AppointmentMonitoring />} />
          <Route path="/prescriptions" element={<PrescriptionLogs />} />
          <Route path="/analytics" element={<AIAnalytics />} />
          <Route path="/settings" element={<SystemSettings />} />
          <Route path="/security" element={<SecurityLogs />} />
        </Routes>
      </AdminLayout>
    </Router>
  );
}

export default App;