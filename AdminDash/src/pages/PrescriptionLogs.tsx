import React, { useState } from 'react';
import { DataTable } from '../components/ui/DataTable';
import { FileText, Eye, Download, Calendar, User, UserCheck } from 'lucide-react';
import { format } from 'date-fns';

const prescriptionData = [
  {
    id: 1,
    patientName: '<PERSON>',
    doctorName: 'Dr. <PERSON>',
    medications: 'Lisinopril 10mg, Metformin 500mg',
    issuedDate: '2024-01-20',
    diagnosis: 'Hypertension, Diabetes Type 2',
    instructions: 'Take once daily with food',
    refills: 2,
    status: 'Active'
  },
  {
    id: 2,
    patientName: '<PERSON>',
    doctorName: 'Dr. <PERSON>',
    medications: 'Amoxicillin 500mg',
    issuedDate: '2024-01-19',
    diagnosis: 'Upper Respiratory Infection',
    instructions: 'Take 3 times daily for 7 days',
    refills: 0,
    status: 'Completed'
  },
  {
    id: 3,
    patientName: '<PERSON>',
    doctorName: 'Dr. <PERSON>',
    medications: 'Atorvastatin 20mg, Aspirin 81mg',
    issuedDate: '2024-01-18',
    diagnosis: 'High Cholesterol',
    instructions: 'Take once daily in the evening',
    refills: 5,
    status: 'Active'
  },
  {
    id: 4,
    patientName: '<PERSON>',
    doctor<PERSON>ame: 'Dr. <PERSON>',
    medications: 'Topical Tretinoin 0.025%',
    issuedDate: '2024-01-17',
    diagnosis: 'Acne Vulgaris',
    instructions: 'Apply thin layer at bedtime',
    refills: 3,
    status: 'Active'
  },
  {
    id: 5,
    patientName: 'David Miller',
    doctorName: 'Dr. Lisa Rodriguez',
    medications: 'Albuterol Inhaler',
    issuedDate: '2024-01-16',
    diagnosis: 'Asthma',
    instructions: '2 puffs as needed for shortness of breath',
    refills: 1,
    status: 'Active'
  },
];

const columns = [
  { key: 'patientName', header: 'Patient', sortable: true },
  { key: 'doctorName', header: 'Doctor', sortable: true },
  { key: 'medications', header: 'Medications', sortable: false },
  { key: 'diagnosis', header: 'Diagnosis', sortable: false },
  { key: 'issuedDate', header: 'Issued Date', sortable: true },
  { key: 'refills', header: 'Refills', sortable: true },
  { key: 'status', header: 'Status', sortable: true },
];

export const PrescriptionLogs: React.FC = () => {
  const [selectedDoctor, setSelectedDoctor] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedDateRange, setSelectedDateRange] = useState('all');

  const uniqueDoctors = [...new Set(prescriptionData.map(p => p.doctorName))];

  const filteredData = prescriptionData.filter(prescription => {
    const doctorMatch = selectedDoctor === 'all' || prescription.doctorName === selectedDoctor;
    const statusMatch = selectedStatus === 'all' || prescription.status.toLowerCase() === selectedStatus;
    
    let dateMatch = true;
    if (selectedDateRange !== 'all') {
      const prescriptionDate = new Date(prescription.issuedDate);
      const today = new Date();
      const daysAgo = parseInt(selectedDateRange);
      const cutoffDate = new Date(today.getTime() - (daysAgo * 24 * 60 * 60 * 1000));
      dateMatch = prescriptionDate >= cutoffDate;
    }
    
    return doctorMatch && statusMatch && dateMatch;
  });

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium";
    if (status === 'Active') {
      return <span className={`${baseClasses} bg-green-100 text-green-800`}>Active</span>;
    } else {
      return <span className={`${baseClasses} bg-gray-100 text-gray-800`}>Completed</span>;
    }
  };

  const enhancedData = filteredData.map(prescription => ({
    ...prescription,
    issuedDate: format(new Date(prescription.issuedDate), 'MMM dd, yyyy'),
    status: getStatusBadge(prescription.status),
    medications: (
      <div className="max-w-xs">
        <p className="text-sm font-medium text-gray-900 truncate" title={prescription.medications}>
          {prescription.medications}
        </p>
      </div>
    ),
    diagnosis: (
      <div className="max-w-xs">
        <p className="text-sm text-gray-600 truncate" title={prescription.diagnosis}>
          {prescription.diagnosis}
        </p>
      </div>
    ),
  }));

  const actions = (row: any) => (
    <div className="flex items-center space-x-2">
      <button 
        className="p-2 text-purple-600 hover:text-purple-800 hover:bg-purple-50 rounded-lg transition-colors"
        title="View Full Prescription"
      >
        <Eye className="h-4 w-4" />
      </button>
      <button 
        className="p-2 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-lg transition-colors"
        title="Download PDF"
      >
        <Download className="h-4 w-4" />
      </button>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Prescription Logs</h1>
          <p className="text-gray-600 mt-2">View and manage all prescription records</p>
        </div>
        <button className="inline-flex items-center bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-2 rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all shadow-lg hover:shadow-xl">
          <Download className="h-4 w-4 mr-2" />
          Export All
        </button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-lg transition-all">
          <div className="flex items-center">
            <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
              <FileText className="h-6 w-6 text-white" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Total Prescriptions</p>
              <p className="text-2xl font-bold text-gray-900">{prescriptionData.length}</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-lg transition-all">
          <div className="flex items-center">
            <div className="p-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg">
              <Calendar className="h-6 w-6 text-white" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">This Week</p>
              <p className="text-2xl font-bold text-gray-900">
                {prescriptionData.filter(p => {
                  const prescDate = new Date(p.issuedDate);
                  const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
                  return prescDate >= weekAgo;
                }).length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-lg transition-all">
          <div className="flex items-center">
            <div className="p-2 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg">
              <User className="h-6 w-6 text-white" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Active Prescriptions</p>
              <p className="text-2xl font-bold text-gray-900">
                {prescriptionData.filter(p => p.status === 'Active').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-lg transition-all">
          <div className="flex items-center">
            <div className="p-2 bg-gradient-to-r from-teal-500 to-cyan-500 rounded-lg">
              <UserCheck className="h-6 w-6 text-white" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Unique Doctors</p>
              <p className="text-2xl font-bold text-gray-900">{uniqueDoctors.length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Filter by Doctor</label>
            <select
              value={selectedDoctor}
              onChange={(e) => setSelectedDoctor(e.target.value)}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              <option value="all">All Doctors</option>
              {uniqueDoctors.map(doctor => (
                <option key={doctor} value={doctor}>{doctor}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Filter by Status</label>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              <option value="all">All Statuses</option>
              <option value="active">Active</option>
              <option value="completed">Completed</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
            <select
              value={selectedDateRange}
              onChange={(e) => setSelectedDateRange(e.target.value)}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              <option value="all">All Time</option>
              <option value="7">Last 7 days</option>
              <option value="30">Last 30 days</option>
              <option value="90">Last 90 days</option>
            </select>
          </div>
        </div>
      </div>

      {/* Data Table */}
      <DataTable
        columns={columns}
        data={enhancedData}
        actions={actions}
        searchable={true}
        filterable={false}
        pagination={true}
      />
    </div>
  );
};