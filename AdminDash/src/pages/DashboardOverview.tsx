import React from 'react';
import { StatCard } from '../components/ui/StatCard';
import { Users, UserCheck, Calendar, Brain, Activity, TrendingUp, Clock } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';

const activityData = [
  { name: 'Mon', users: 1200, appointments: 89, aiSessions: 156 },
  { name: '<PERSON><PERSON>', users: 1350, appointments: 94, aiSessions: 178 },
  { name: 'Wed', users: 1100, appointments: 76, aiSessions: 134 },
  { name: 'Thu', users: 1450, appointments: 102, aiSessions: 198 },
  { name: 'Fri', users: 1600, appointments: 118, aiSessions: 224 },
  { name: 'Sat', users: 800, appointments: 45, aiSessions: 89 },
  { name: 'Sun', users: 600, appointments: 32, aiSessions: 67 }
];

const recentActivities = [
  { id: 1, type: 'approval', message: 'Dr. <PERSON> approved for Cardiology', time: '2 minutes ago', status: 'success' },
  { id: 2, type: 'user', message: 'New patient registration: <PERSON>', time: '5 minutes ago', status: 'info' },
  { id: 3, type: 'appointment', message: 'Emergency appointment scheduled', time: '12 minutes ago', status: 'warning' },
  { id: 4, type: 'ai', message: 'AI consultation completed for Patient #4521', time: '18 minutes ago', status: 'success' },
  { id: 5, type: 'system', message: 'System backup completed successfully', time: '1 hour ago', status: 'info' },
];

export const DashboardOverview: React.FC = () => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Dashboard Overview</h1>
        <p className="text-gray-600 mt-2">Welcome back! Here's what's happening with your platform today.</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Users"
          value="12,847"
          change="+12.5%"
          changeType="increase"
          icon={Users}
          iconColor="text-purple-600"
        />
        <StatCard
          title="Approved Doctors"
          value="342"
          change="+8 new"
          changeType="increase"
          icon={UserCheck}
          iconColor="text-green-600"
        />
        <StatCard
          title="Appointments Today"
          value="89"
          change="+23.1%"
          changeType="increase"
          icon={Calendar}
          iconColor="text-blue-600"
        />
        <StatCard
          title="AI Sessions Active"
          value="156"
          change="+18.2%"
          changeType="increase"
          icon={Brain}
          iconColor="text-teal-600"
        />
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Activity Chart */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Weekly Activity</h3>
              <p className="text-sm text-gray-600">User engagement and system usage</p>
            </div>
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              <span className="text-sm font-medium text-green-600">+15.3%</span>
            </div>
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={activityData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis dataKey="name" stroke="#6b7280" fontSize={12} />
              <YAxis stroke="#6b7280" fontSize={12} />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: 'white', 
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                }}
              />
              <Area type="monotone" dataKey="users" stackId="1" stroke="#8b5cf6" fill="#8b5cf6" fillOpacity={0.1} />
              <Area type="monotone" dataKey="appointments" stackId="1" stroke="#ec4899" fill="#ec4899" fillOpacity={0.1} />
              <Area type="monotone" dataKey="aiSessions" stackId="1" stroke="#14b8a6" fill="#14b8a6" fillOpacity={0.1} />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
              <p className="text-sm text-gray-600">Latest system events and updates</p>
            </div>
            <Activity className="h-5 w-5 text-gray-400" />
          </div>
          <div className="space-y-4">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-purple-50/50 transition-colors">
                <div className={`flex-shrink-0 w-2 h-2 rounded-full mt-2 ${
                  activity.status === 'success' ? 'bg-green-500' :
                  activity.status === 'warning' ? 'bg-yellow-500' :
                  'bg-purple-500'
                }`} />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">{activity.message}</p>
                  <div className="flex items-center mt-1">
                    <Clock className="h-3 w-3 text-gray-400 mr-1" />
                    <p className="text-xs text-gray-500">{activity.time}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-4 pt-4 border-t border-gray-200">
            <button className="text-sm font-medium bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent hover:from-purple-700 hover:to-pink-700">
              View all activities →
            </button>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <button className="flex items-center justify-center p-4 border-2 border-dashed border-purple-300 rounded-lg hover:border-purple-500 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 transition-all group">
            <div className="text-center">
              <UserCheck className="h-8 w-8 text-purple-400 group-hover:text-purple-600 mx-auto mb-2" />
              <span className="text-sm font-medium text-gray-700 group-hover:text-purple-700">Review Doctors</span>
            </div>
          </button>
          <button className="flex items-center justify-center p-4 border-2 border-dashed border-purple-300 rounded-lg hover:border-purple-500 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 transition-all group">
            <div className="text-center">
              <Calendar className="h-8 w-8 text-purple-400 group-hover:text-purple-600 mx-auto mb-2" />
              <span className="text-sm font-medium text-gray-700 group-hover:text-purple-700">View Appointments</span>
            </div>
          </button>
          <button className="flex items-center justify-center p-4 border-2 border-dashed border-purple-300 rounded-lg hover:border-purple-500 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 transition-all group">
            <div className="text-center">
              <Brain className="h-8 w-8 text-purple-400 group-hover:text-purple-600 mx-auto mb-2" />
              <span className="text-sm font-medium text-gray-700 group-hover:text-purple-700">AI Analytics</span>
            </div>
          </button>
          <button className="flex items-center justify-center p-4 border-2 border-dashed border-purple-300 rounded-lg hover:border-purple-500 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 transition-all group">
            <div className="text-center">
              <Users className="h-8 w-8 text-purple-400 group-hover:text-purple-600 mx-auto mb-2" />
              <span className="text-sm font-medium text-gray-700 group-hover:text-purple-700">Manage Users</span>
            </div>
          </button>
        </div>
      </div>
    </div>
  );
};