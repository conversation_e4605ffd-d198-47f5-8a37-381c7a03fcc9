import React, { useState } from 'react';
import { DataTable } from '../components/ui/DataTable';
import { Shield, AlertTriangle, CheckCircle, XCircle, Eye, Download, Calendar } from 'lucide-react';
import { format } from 'date-fns';

const securityLogsData = [
  {
    id: 1,
    timestamp: '2024-01-22 14:32:15',
    event: 'Successful Login',
    user: '<EMAIL>',
    ip: '*************',
    location: 'New York, NY',
    severity: 'info',
    details: 'Admin login successful'
  },
  {
    id: 2,
    timestamp: '2024-01-22 14:25:33',
    event: 'Failed Login Attempt',
    user: '<EMAIL>',
    ip: '************',
    location: 'Unknown',
    severity: 'warning',
    details: 'Multiple failed login attempts detected'
  },
  {
    id: 3,
    timestamp: '2024-01-22 13:45:22',
    event: 'Password Changed',
    user: 'dr.joh<PERSON>@medconnect.ai',
    ip: '*************',
    location: 'Boston, MA',
    severity: 'info',
    details: 'User changed password successfully'
  },
  {
    id: 4,
    timestamp: '2024-01-22 12:15:44',
    event: 'Suspicious Activity',
    user: '<EMAIL>',
    ip: '************',
    location: 'Unknown',
    severity: 'high',
    details: 'Unusual access pattern detected'
  },
  {
    id: 5,
    timestamp: '2024-01-22 11:30:12',
    event: 'Account Locked',
    user: '<EMAIL>',
    ip: '*************',
    location: 'Chicago, IL',
    severity: 'medium',
    details: 'Account locked due to multiple failed attempts'
  },
  {
    id: 6,
    timestamp: '2024-01-22 10:45:18',
    event: 'Data Access',
    user: '<EMAIL>',
    ip: '*************',
    location: 'Los Angeles, CA',
    severity: 'info',
    details: 'Accessed patient records'
  },
  {
    id: 7,
    timestamp: '2024-01-22 09:22:33',
    event: 'Permission Change',
    user: '<EMAIL>',
    ip: '*************',
    location: 'New York, NY',
    severity: 'medium',
    details: 'Modified user permissions'
  },
  {
    id: 8,
    timestamp: '2024-01-21 18:15:44',
    event: 'System Backup',
    user: 'system',
    ip: 'localhost',
    location: 'Server',
    severity: 'info',
    details: 'Automated system backup completed'
  },
];

const columns = [
  { key: 'timestamp', header: 'Timestamp', sortable: true },
  { key: 'event', header: 'Event', sortable: true },
  { key: 'user', header: 'User', sortable: true },
  { key: 'ip', header: 'IP Address', sortable: false },
  { key: 'location', header: 'Location', sortable: false },
  { key: 'severity', header: 'Severity', sortable: true },
];

export const SecurityLogs: React.FC = () => {
  const [selectedSeverity, setSelectedSeverity] = useState('all');
  const [selectedTimeRange, setSelectedTimeRange] = useState('24h');

  const filteredData = securityLogsData.filter(log => {
    const severityMatch = selectedSeverity === 'all' || log.severity === selectedSeverity;
    
    let timeMatch = true;
    if (selectedTimeRange !== 'all') {
      const logTime = new Date(log.timestamp);
      const now = new Date();
      const hoursAgo = parseInt(selectedTimeRange.replace('h', ''));
      const cutoffTime = new Date(now.getTime() - (hoursAgo * 60 * 60 * 1000));
      timeMatch = logTime >= cutoffTime;
    }
    
    return severityMatch && timeMatch;
  });

  const getSeverityBadge = (severity: string) => {
    const baseClasses = "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium";
    switch (severity) {
      case 'high':
        return (
          <span className={`${baseClasses} bg-red-100 text-red-800`}>
            <AlertTriangle className="h-3 w-3 mr-1" />
            High
          </span>
        );
      case 'medium':
        return (
          <span className={`${baseClasses} bg-yellow-100 text-yellow-800`}>
            <AlertTriangle className="h-3 w-3 mr-1" />
            Medium
          </span>
        );
      case 'warning':
        return (
          <span className={`${baseClasses} bg-orange-100 text-orange-800`}>
            <XCircle className="h-3 w-3 mr-1" />
            Warning
          </span>
        );
      default:
        return (
          <span className={`${baseClasses} bg-purple-100 text-purple-800`}>
            <CheckCircle className="h-3 w-3 mr-1" />
            Info
          </span>
        );
    }
  };

  const enhancedData = filteredData.map(log => ({
    ...log,
    timestamp: format(new Date(log.timestamp), 'MMM dd, yyyy HH:mm:ss'),
    severity: getSeverityBadge(log.severity),
  }));

  const actions = (row: any) => (
    <div className="flex items-center space-x-2">
      <button 
        className="p-2 text-purple-600 hover:text-purple-800 hover:bg-purple-50 rounded-lg transition-colors"
        title="View Details"
      >
        <Eye className="h-4 w-4" />
      </button>
    </div>
  );

  const exportLogs = () => {
    console.log('Exporting security logs...');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Security Logs</h1>
          <p className="text-gray-600 mt-2">Monitor security events and access patterns</p>
        </div>
        <button 
          onClick={exportLogs}
          className="inline-flex items-center bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-2 rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all shadow-lg hover:shadow-xl"
        >
          <Download className="h-4 w-4 mr-2" />
          Export Logs
        </button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-lg transition-all">
          <div className="flex items-center">
            <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
              <Shield className="h-6 w-6 text-white" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Total Events (24h)</p>
              <p className="text-2xl font-bold text-gray-900">
                {securityLogsData.length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-lg transition-all">
          <div className="flex items-center">
            <div className="p-2 bg-gradient-to-r from-red-500 to-pink-500 rounded-lg">
              <AlertTriangle className="h-6 w-6 text-white" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">High Severity</p>
              <p className="text-2xl font-bold text-gray-900">
                {securityLogsData.filter(log => log.severity === 'high').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-lg transition-all">
          <div className="flex items-center">
            <div className="p-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg">
              <XCircle className="h-6 w-6 text-white" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Failed Logins</p>
              <p className="text-2xl font-bold text-gray-900">
                {securityLogsData.filter(log => log.event.includes('Failed')).length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-lg transition-all">
          <div className="flex items-center">
            <div className="p-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg">
              <CheckCircle className="h-6 w-6 text-white" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Successful Logins</p>
              <p className="text-2xl font-bold text-gray-900">
                {securityLogsData.filter(log => log.event.includes('Successful')).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Filter by Severity</label>
            <select
              value={selectedSeverity}
              onChange={(e) => setSelectedSeverity(e.target.value)}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              <option value="all">All Severities</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="warning">Warning</option>
              <option value="info">Info</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Time Range</label>
            <select
              value={selectedTimeRange}
              onChange={(e) => setSelectedTimeRange(e.target.value)}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              <option value="1h">Last Hour</option>
              <option value="24h">Last 24 Hours</option>
              <option value="168h">Last Week</option>
              <option value="all">All Time</option>
            </select>
          </div>
          <div className="flex items-end">
            <button className="w-full inline-flex items-center justify-center px-4 py-2 bg-gradient-to-r from-red-600 to-pink-600 text-white rounded-lg hover:from-red-700 hover:to-pink-700 transition-all shadow-lg hover:shadow-xl">
              <AlertTriangle className="h-4 w-4 mr-2" />
              Security Alert
            </button>
          </div>
        </div>
      </div>

      {/* Security Alerts */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Security Alerts</h3>
        <div className="space-y-3">
          <div className="flex items-center p-3 bg-red-50 rounded-lg border border-red-200">
            <AlertTriangle className="h-5 w-5 text-red-600 mr-3 flex-shrink-0" />
            <div className="flex-1">
              <p className="text-sm font-medium text-red-900">Suspicious Activity Detected</p>
              <p className="text-xs text-red-700">Multiple failed login attempts from IP ************</p>
            </div>
            <span className="text-xs text-red-600">2 min ago</span>
          </div>
          <div className="flex items-center p-3 bg-yellow-50 rounded-lg border border-yellow-200">
            <AlertTriangle className="h-5 w-5 text-yellow-600 mr-3 flex-shrink-0" />
            <div className="flex-1">
              <p className="text-sm font-medium text-yellow-900">Account Lockout</p>
              <p className="text-xs text-yellow-700"><NAME_EMAIL> has been locked</p>
            </div>
            <span className="text-xs text-yellow-600">15 min ago</span>
          </div>
          <div className="flex items-center p-3 bg-purple-50 rounded-lg border border-purple-200">
            <CheckCircle className="h-5 w-5 text-purple-600 mr-3 flex-shrink-0" />
            <div className="flex-1">
              <p className="text-sm font-medium text-purple-900">System Backup Completed</p>
              <p className="text-xs text-purple-700">Daily security backup completed successfully</p>
            </div>
            <span className="text-xs text-purple-600">1 hour ago</span>
          </div>
        </div>
      </div>

      {/* Data Table */}
      <DataTable
        columns={columns}
        data={enhancedData}
        actions={actions}
        searchable={true}
        filterable={false}
        pagination={true}
      />
    </div>
  );
};