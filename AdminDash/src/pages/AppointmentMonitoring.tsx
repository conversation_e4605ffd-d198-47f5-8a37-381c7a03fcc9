import React, { useState } from 'react';
import { DataTable } from '../components/ui/DataTable';
import { Calendar, Clock, CheckCircle, XCircle, Download, Filter } from 'lucide-react';
import { format } from 'date-fns';

const appointmentData = [
  {
    id: 1,
    patientName: '<PERSON>',
    doctorName: 'Dr. <PERSON>',
    specialty: 'Cardiology',
    date: '2024-01-22',
    time: '10:00 AM',
    status: 'Scheduled',
    type: 'In-Person',
    duration: '30 min'
  },
  {
    id: 2,
    patientName: '<PERSON>',
    doctorName: 'Dr. <PERSON>',
    specialty: 'Pediatrics',
    date: '2024-01-22',
    time: '11:30 AM',
    status: 'Completed',
    type: 'Telemedicine',
    duration: '45 min'
  },
  {
    id: 3,
    patientName: '<PERSON>',
    doctorName: 'Dr. <PERSON>',
    specialty: 'Neurology',
    date: '2024-01-22',
    time: '2:00 PM',
    status: 'Cancelled',
    type: 'In-Person',
    duration: '60 min'
  },
  {
    id: 4,
    patientName: '<PERSON>',
    doctorName: 'Dr. <PERSON>',
    specialty: 'Dermatology',
    date: '2024-01-23',
    time: '9:00 AM',
    status: 'Scheduled',
    type: 'In-Person',
    duration: '30 min'
  },
  {
    id: 5,
    patient<PERSON>ame: '<PERSON> <PERSON>',
    doctor<PERSON><PERSON>: '<PERSON>. <PERSON>',
    specialty: 'Pediatrics',
    date: '2024-01-23',
    time: '3:30 PM',
    status: 'No Show',
    type: 'Telemedicine',
    duration: '30 min'
  },
];

const columns = [
  { key: 'patient<PERSON>ame', header: 'Patient', sortable: true },
  { key: 'doctor<PERSON>ame', header: 'Doctor', sortable: true },
  { key: 'specialty', header: 'Specialty', sortable: true },
  { key: 'date', header: 'Date', sortable: true },
  { key: 'time', header: 'Time', sortable: true },
  { key: 'status', header: 'Status', sortable: true },
  { key: 'type', header: 'Type', sortable: true },
  { key: 'duration', header: 'Duration', sortable: false },
];

export const AppointmentMonitoring: React.FC = () => {
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedDate, setSelectedDate] = useState('');

  const filteredData = appointmentData.filter(appointment => {
    const statusMatch = selectedStatus === 'all' || appointment.status.toLowerCase() === selectedStatus;
    const dateMatch = !selectedDate || appointment.date === selectedDate;
    return statusMatch && dateMatch;
  });

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium";
    switch (status) {
      case 'Scheduled':
        return (
          <span className={`${baseClasses} bg-purple-100 text-purple-800`}>
            <Clock className="h-3 w-3 mr-1" />
            Scheduled
          </span>
        );
      case 'Completed':
        return (
          <span className={`${baseClasses} bg-green-100 text-green-800`}>
            <CheckCircle className="h-3 w-3 mr-1" />
            Completed
          </span>
        );
      case 'Cancelled':
        return (
          <span className={`${baseClasses} bg-red-100 text-red-800`}>
            <XCircle className="h-3 w-3 mr-1" />
            Cancelled
          </span>
        );
      case 'No Show':
        return (
          <span className={`${baseClasses} bg-orange-100 text-orange-800`}>
            <XCircle className="h-3 w-3 mr-1" />
            No Show
          </span>
        );
      default:
        return <span className={`${baseClasses} bg-gray-100 text-gray-800`}>{status}</span>;
    }
  };

  const getTypeBadge = (type: string) => {
    const baseClasses = "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium";
    if (type === 'Telemedicine') {
      return <span className={`${baseClasses} bg-purple-100 text-purple-800`}>{type}</span>;
    } else {
      return <span className={`${baseClasses} bg-gray-100 text-gray-800`}>{type}</span>;
    }
  };

  const enhancedData = filteredData.map(appointment => ({
    ...appointment,
    date: format(new Date(appointment.date), 'MMM dd, yyyy'),
    status: getStatusBadge(appointment.status),
    type: getTypeBadge(appointment.type),
  }));

  const exportData = () => {
    // This would typically generate and download a CSV/PDF file
    console.log('Exporting appointment data...');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Appointment Monitoring</h1>
          <p className="text-gray-600 mt-2">Track and manage all appointments across the platform</p>
        </div>
        <button 
          onClick={exportData}
          className="inline-flex items-center bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-2 rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all shadow-lg hover:shadow-xl"
        >
          <Download className="h-4 w-4 mr-2" />
          Export Data
        </button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-lg transition-all">
          <div className="flex items-center">
            <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
              <Calendar className="h-6 w-6 text-white" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Total Today</p>
              <p className="text-2xl font-bold text-gray-900">
                {appointmentData.filter(a => a.date === '2024-01-22').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-lg transition-all">
          <div className="flex items-center">
            <div className="p-2 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg">
              <Clock className="h-6 w-6 text-white" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Scheduled</p>
              <p className="text-2xl font-bold text-gray-900">
                {appointmentData.filter(a => a.status === 'Scheduled').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-lg transition-all">
          <div className="flex items-center">
            <div className="p-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg">
              <CheckCircle className="h-6 w-6 text-white" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-gray-900">
                {appointmentData.filter(a => a.status === 'Completed').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-lg transition-all">
          <div className="flex items-center">
            <div className="p-2 bg-gradient-to-r from-red-500 to-pink-500 rounded-lg">
              <XCircle className="h-6 w-6 text-white" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Cancelled</p>
              <p className="text-2xl font-bold text-gray-900">
                {appointmentData.filter(a => a.status === 'Cancelled').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-lg transition-all">
          <div className="flex items-center">
            <div className="p-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg">
              <XCircle className="h-6 w-6 text-white" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">No Shows</p>
              <p className="text-2xl font-bold text-gray-900">
                {appointmentData.filter(a => a.status === 'No Show').length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-2">Filter by Status</label>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              <option value="all">All Statuses</option>
              <option value="scheduled">Scheduled</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
              <option value="no show">No Show</option>
            </select>
          </div>
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-2">Filter by Date</label>
            <input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>
          <div className="flex-1 flex items-end">
            <button className="w-full inline-flex items-center justify-center px-4 py-2 border border-purple-300 text-purple-700 rounded-lg hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 transition-all">
              <Filter className="h-4 w-4 mr-2" />
              More Filters
            </button>
          </div>
        </div>
      </div>

      {/* Data Table */}
      <DataTable
        columns={columns}
        data={enhancedData}
        searchable={true}
        filterable={false}
        pagination={true}
      />
    </div>
  );
};