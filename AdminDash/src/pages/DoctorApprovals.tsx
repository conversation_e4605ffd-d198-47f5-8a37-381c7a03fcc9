import React from 'react';
import { DataTable } from '../components/ui/DataTable';
import { Check, X, <PERSON>, Clock, <PERSON><PERSON><PERSON><PERSON><PERSON>, AlertCircle } from 'lucide-react';

const pendingDoctors = [
  {
    id: 1,
    name: 'Dr. <PERSON>',
    email: 'micha<PERSON>.<EMAIL>',
    specialization: 'Cardiology',
    experience: '8 years',
    license: 'MD-12345-CA',
    submittedDate: '2024-01-18',
    documents: 4,
    status: 'Pending'
  },
  {
    id: 2,
    name: 'Dr. <PERSON>',
    email: 'lisa.rod<PERSON>ue<PERSON>@email.com',
    specialization: 'Pediatrics',
    experience: '6 years',
    license: 'MD-67890-NY',
    submittedDate: '2024-01-17',
    documents: 3,
    status: 'Under Review'
  },
  {
    id: 3,
    name: 'Dr. <PERSON>',
    email: '<EMAIL>',
    specialization: 'Neurology',
    experience: '12 years',
    license: 'MD-54321-TX',
    submittedDate: '2024-01-16',
    documents: 5,
    status: 'Pending'
  },
  {
    id: 4,
    name: 'Dr. <PERSON>',
    email: '<EMAIL>',
    specialization: 'Dermatology',
    experience: '4 years',
    license: 'MD-98765-FL',
    submittedDate: '2024-01-15',
    documents: 4,
    status: 'Documents Required'
  },
];

const columns = [
  { key: 'name', header: 'Doctor Name', sortable: true },
  { key: 'email', header: 'Email', sortable: true },
  { key: 'specialization', header: 'Specialization', sortable: true },
  { key: 'experience', header: 'Experience', sortable: true },
  { key: 'license', header: 'License', sortable: false },
  { key: 'submittedDate', header: 'Submitted', sortable: true },
  { key: 'status', header: 'Status', sortable: true },
];

export const DoctorApprovals: React.FC = () => {
  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium";
    switch (status) {
      case 'Pending':
        return (
          <span className={`${baseClasses} bg-yellow-100 text-yellow-800`}>
            <Clock className="h-3 w-3 mr-1" />
            Pending
          </span>
        );
      case 'Under Review':
        return (
          <span className={`${baseClasses} bg-purple-100 text-purple-800`}>
            <Eye className="h-3 w-3 mr-1" />
            Under Review
          </span>
        );
      case 'Documents Required':
        return (
          <span className={`${baseClasses} bg-red-100 text-red-800`}>
            <AlertCircle className="h-3 w-3 mr-1" />
            Docs Required
          </span>
        );
      default:
        return <span className={`${baseClasses} bg-gray-100 text-gray-800`}>{status}</span>;
    }
  };

  const enhancedData = pendingDoctors.map(doctor => ({
    ...doctor,
    status: getStatusBadge(doctor.status),
  }));

  const actions = (row: any) => (
    <div className="flex items-center space-x-2">
      <button 
        className="p-2 text-purple-600 hover:text-purple-800 hover:bg-purple-50 rounded-lg transition-colors"
        title="View Details"
      >
        <Eye className="h-4 w-4" />
      </button>
      <button 
        className="p-2 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-lg transition-colors"
        title="Approve"
      >
        <Check className="h-4 w-4" />
      </button>
      <button 
        className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors"
        title="Reject"
      >
        <X className="h-4 w-4" />
      </button>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Doctor Approvals</h1>
          <p className="text-gray-600 mt-2">Review and approve doctor registration requests</p>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4 hover:shadow-md transition-shadow">
          <div className="flex items-center">
            <div className="p-2 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg">
              <Clock className="h-6 w-6 text-white" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Pending Review</p>
              <p className="text-2xl font-bold text-gray-900">
                {pendingDoctors.filter(d => d.status === 'Pending').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4 hover:shadow-md transition-shadow">
          <div className="flex items-center">
            <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
              <Eye className="h-6 w-6 text-white" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Under Review</p>
              <p className="text-2xl font-bold text-gray-900">
                {pendingDoctors.filter(d => d.status === 'Under Review').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4 hover:shadow-md transition-shadow">
          <div className="flex items-center">
            <div className="p-2 bg-gradient-to-r from-red-500 to-pink-500 rounded-lg">
              <AlertCircle className="h-6 w-6 text-white" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Docs Required</p>
              <p className="text-2xl font-bold text-gray-900">
                {pendingDoctors.filter(d => d.status === 'Documents Required').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4 hover:shadow-md transition-shadow">
          <div className="flex items-center">
            <div className="p-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg">
              <UserCheck className="h-6 w-6 text-white" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Approved Today</p>
              <p className="text-2xl font-bold text-gray-900">3</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="flex flex-wrap gap-3">
          <button className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all shadow-lg hover:shadow-xl">
            <Check className="h-4 w-4 mr-2" />
            Bulk Approve Selected
          </button>
          <button className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all shadow-lg hover:shadow-xl">
            <Eye className="h-4 w-4 mr-2" />
            Review Next in Queue
          </button>
          <button className="inline-flex items-center px-4 py-2 border border-purple-300 text-purple-700 rounded-lg hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 transition-all">
            Export Pending List
          </button>
        </div>
      </div>

      {/* Data Table */}
      <DataTable
        columns={columns}
        data={enhancedData}
        actions={actions}
        searchable={true}
        filterable={true}
        pagination={true}
      />
    </div>
  );
};