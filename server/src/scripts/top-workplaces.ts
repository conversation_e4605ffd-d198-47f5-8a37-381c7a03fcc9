// Base URL for the API
const BASE_URL = 'http://localhost:3000';

// Types based on the existing schemas
interface Shift {
  id: number;
  createdAt: string;
  startAt: string;
  endAt: string;
  workplaceId: number;
  workerId: number | null;
  cancelledAt: string | null;
}

interface Workplace {
  id: number;
  name: string;
  status: number;
}

interface PaginatedResponse<T> {
  data: T[];
  links: { next?: string };
}

interface WorkplaceShiftCount {
  name: string;
  shifts: number;
}

/**
 * Fetches all data from a paginated endpoint
 */
async function fetchAllPaginated<T>(endpoint: string): Promise<T[]> {
  const allData: T[] = [];
  let nextUrl: string | undefined = `${BASE_URL}${endpoint}`;

  while (nextUrl) {
    try {
      const response = await fetch(nextUrl);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const responseData: PaginatedResponse<T> = await response.json();
      allData.push(...responseData.data);

      // Get the next page URL if it exists
      nextUrl = responseData.links.next;
    } catch (error) {
      console.error(`Error fetching data from ${nextUrl}:`, error);
      throw error;
    }
  }

  return allData;
}

/**
 * Main function to fetch and process workplace data
 */
async function getTopWorkplaces(): Promise<WorkplaceShiftCount[]> {
  try {
    // Fetch all shifts and workplaces concurrently
    const [allShifts, allWorkplaces] = await Promise.all([
      fetchAllPaginated<Shift>('/shifts'),
      fetchAllPaginated<Workplace>('/workplaces')
    ]);

    // Filter for completed shifts (those with a workerId)
    const completedShifts = allShifts.filter(shift => shift.workerId !== null);

    // Count shifts per workplace
    const shiftCounts = new Map<number, number>();
    completedShifts.forEach(shift => {
      const currentCount = shiftCounts.get(shift.workplaceId) || 0;
      shiftCounts.set(shift.workplaceId, currentCount + 1);
    });

    // Create a map of workplace ID to workplace name
    const workplaceMap = new Map<number, string>();
    allWorkplaces.forEach(workplace => {
      workplaceMap.set(workplace.id, workplace.name);
    });

    // Convert to array and sort by shift count (descending)
    const workplaceShiftCounts: WorkplaceShiftCount[] = [];
    shiftCounts.forEach((shiftCount, workplaceId) => {
      const workplaceName = workplaceMap.get(workplaceId);
      if (workplaceName) {
        workplaceShiftCounts.push({
          name: workplaceName,
          shifts: shiftCount
        });
      }
    });

    // Sort by shift count (descending) and take top 3
    workplaceShiftCounts.sort((a, b) => b.shifts - a.shifts);
    return workplaceShiftCounts.slice(0, 3);

  } catch (error) {
    console.error('Error fetching top workplaces:', error);
    throw error;
  }
}

/**
 * Main execution
 */
async function main() {
  try {
    const topWorkplaces = await getTopWorkplaces();
    console.log(JSON.stringify(topWorkplaces, null, 2));
  } catch (error) {
    console.error('Failed to get top workplaces:', error);
    process.exit(1);
  }
}

main();
